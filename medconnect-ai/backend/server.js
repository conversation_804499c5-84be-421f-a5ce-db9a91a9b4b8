require('dotenv').config();
const express = require("express");
const cors = require("cors");
const connectDB = require("./config/db");
const authRoutes = require("./routes/authRoutes");
const dashboardRoutes = require("./routes/dashboard.routes");
const appointmentRoutes = require("./routes/appointmentRoutes");
const userRoutes = require("./routes/userRoutes");
const prescriptionRoutes = require("./routes/prescriptionRoutes");
const aiRoutes = require("./routes/aiRoutes");

// Set default JWT secret if not in environment
process.env.JWT_SECRET = process.env.JWT_SECRET || "858085";

const app = express();

// Middleware
app.use(cors());
app.use(express.json());

// Add request logging middleware
app.use((req, res, next) => {
  console.log(`${new Date().toISOString()} - ${req.method} ${req.url}`);
  next();
});

// Mount routes
app.use("/api/auth", authRoutes);
app.use("/api", dashboardRoutes);  // This will handle all dashboard routes
app.use("/api/appointments", appointmentRoutes);
app.use("/api", userRoutes);  // This will handle the /api/doctors route
app.use("/api/prescriptions", prescriptionRoutes);
app.use("/api/ai", aiRoutes);

// Test route
app.get("/api/test", (req, res) => {
  res.json({ message: "API is working" });
});

// Connect to MongoDB Atlas
connectDB();

const PORT = process.env.PORT || 5000;
app.listen(PORT, () => {
  console.log(`Server running on port ${PORT}`);
  console.log("Available routes:");
  console.log("- POST /api/auth/login");
  console.log("- POST /api/auth/signup");
  console.log("- GET /api/test");
  console.log("- GET /api/patient/dashboard");
  console.log("- GET /api/doctor/dashboard");
  console.log("- GET /api/admin/dashboard");
  console.log("- POST /api/appointments/book");
  console.log("- GET /api/appointments/patient");
  console.log("- GET /api/appointments/doctor");
  console.log("- PUT /api/appointments/update-status/:id");
  console.log("- POST /api/prescriptions/add");
  console.log("- GET /api/prescriptions/patient");
  console.log("- GET /api/prescriptions/doctor");
  console.log("- POST /api/ai/symptom");
});
