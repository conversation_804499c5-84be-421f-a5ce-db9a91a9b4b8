nohup: ignoring input
Server running on port 5000
Available routes:
- POST /api/login
- GET /api/doctor/dashboard
- GET /api/test
MongoDB connection error: MongooseServerSelectionError: connect ECONNREFUSED 127.0.0.1:27017
    at _handleConnectionErrors (/home/<USER>/medconnect-ai/backend/node_modules/mongoose/lib/connection.js:1165:11)
    at NativeConnection.openUri (/home/<USER>/medconnect-ai/backend/node_modules/mongoose/lib/connection.js:1096:11) {
  errorLabelSet: Set(0) {},
  reason: TopologyDescription {
    type: 'Unknown',
    servers: Map(1) { 'localhost:27017' => [ServerDescription] },
    stale: false,
    compatible: true,
    heartbeatFrequencyMS: 10000,
    localThresholdMS: 15,
    setName: null,
    maxElectionId: null,
    maxSetVersion: null,
    commonWireVersion: 0,
    logicalSessionTimeoutMinutes: null
  },
  code: undefined
}
