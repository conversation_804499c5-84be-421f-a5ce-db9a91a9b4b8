const express = require("express");
const router = express.Router();
const { authenticateToken } = require("../middleware/auth");
const allowRoles = require("../middleware/role");
const {
  bookAppointment,
  getPatientAppointments,
  getDoctorAppointments,
  updateAppointmentStatus
} = require("../controllers/appointmentController");

router.post("/book", authenticateToken, allowRoles("patient"), bookAppointment);
router.get("/patient", authenticateToken, allowRoles("patient"), getPatientAppointments);
router.get("/doctor", authenticateToken, allowRoles("doctor"), getDoctorAppointments);
router.put("/update-status/:id", authenticateToken, allowRoles("doctor"), updateAppointmentStatus);

module.exports = router; 