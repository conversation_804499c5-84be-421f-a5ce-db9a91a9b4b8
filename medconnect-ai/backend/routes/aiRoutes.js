const express = require("express");
const axios = require("axios");
const router = express.Router();
const { authenticateToken } = require("../middleware/auth");

// POST /api/ai/symptom
router.post("/symptom", authenticateToken, async (req, res) => {
  console.log("=== SYMPTOM ANALYSIS REQUEST ===");
  console.log("User:", req.user?.name, "Role:", req.user?.role);
  console.log("Request body:", req.body);
  console.log("Headers:", req.headers.authorization ? "Token present" : "No token");

  const { symptoms } = req.body;
  if (!symptoms) return res.status(400).json({ error: "No symptoms provided" });

  const symptomList = Array.isArray(symptoms)
    ? symptoms.join(", ")
    : symptoms;

  // Debug: Check environment variables
  console.log("Environment check:", {
    hasApiKey: !!process.env.GEMINI_API_KEY,
    apiUrl: process.env.GEMINI_API_URL,
    nodeEnv: process.env.NODE_ENV
  });

  // Check if API key is configured
  if (!process.env.GEMINI_API_KEY) {
    console.error("Gemini API key not configured");
    return res.status(500).json({ error: "AI service not properly configured" });
  }

  // Gemini-style prompt
  const prompt = `As an AI medical assistant, analyze these symptoms: ${symptomList}.

Please provide:
1. Top 3 possible medical conditions ranked by likelihood
2. Brief explanation for each condition
3. General advice (when to seek immediate medical attention)

Format the response in a clear, structured way.

IMPORTANT: This is for educational purposes only and should not replace professional medical advice.`;

  try {
    // Construct the Gemini API URL with the API key
    const apiUrl = `${process.env.GEMINI_API_URL}?key=${process.env.GEMINI_API_KEY}`;

    console.log("Making request to Gemini API...");
    console.log("API URL (without key):", process.env.GEMINI_API_URL);

    const requestBody = {
      contents: [{
        parts: [{
          text: prompt
        }]
      }]
    };

    // Debug: Log request configuration (without API key)
    console.log("Request configuration:", {
      url: process.env.GEMINI_API_URL,
      bodyStructure: JSON.stringify(requestBody, null, 2)
    });

    const response = await axios.post(
      apiUrl,
      requestBody,
      {
        headers: {
          "Content-Type": "application/json"
        }
      }
    );

    // Debug: Log response structure
    console.log("Gemini API Response Structure:", {
      status: response.status,
      hasData: !!response.data,
      dataKeys: Object.keys(response.data)
    });

    // Extract text from Gemini response
    const analysis = response.data.candidates?.[0]?.content?.parts?.[0]?.text ||
                    response.data.candidates?.[0]?.text ||
                    response.data.text ||
                    "Unable to analyze symptoms at this time.";

    console.log("Successfully processed response");
    res.json({ analysis });
  } catch (err) {
    // Enhanced error logging
    console.error("Gemini API error details:", {
      message: err.message,
      response: err.response?.data,
      status: err.response?.status,
      headers: err.response?.headers,
      config: {
        url: err.config?.url?.replace(process.env.GEMINI_API_KEY, '[REDACTED]'),
        method: err.config?.method,
        headers: err.config?.headers
      },
      stack: err.stack
    });

    // Send appropriate error message
    res.status(500).json({
      error: "AI service error",
      details: process.env.NODE_ENV === 'development'
        ? {
            message: err.message,
            response: err.response?.data,
            status: err.response?.status
          }
        : "Failed to analyze symptoms. Please try again later."
    });
  }
});

module.exports = router;