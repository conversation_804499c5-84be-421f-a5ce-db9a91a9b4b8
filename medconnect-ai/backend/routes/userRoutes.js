const express = require("express");
const router = express.Router();
const { authenticateToken } = require("../middleware/auth");
const allowRoles = require("../middleware/role");
const { getDoctors } = require('../controllers/userController');
const User = require('../models/User');

// Dashboard routes are now handled by dashboard.routes.js
// Removed dummy dashboard responses to avoid conflicts

// Route to get all doctors (accessible to authenticated users)
router.get('/doctors', authenticateToken, getDoctors);

// Get all doctors
router.get('/doctors', async (req, res) => {
  try {
    const doctors = await User.find({ role: 'doctor' })
      .select('name specialization experience');
    res.json(doctors);
  } catch (err) {
    res.status(500).json({ message: 'Error fetching doctors' });
  }
});

// Search users by name and role
router.get('/users/search', authenticateToken, allowRoles('doctor', 'admin'), async (req, res) => {
  try {
    const { query, role } = req.query;
    if (!query) return res.json([]);

    const users = await User.find({
      name: { $regex: query, $options: 'i' },
      role: role || 'patient'
    })
    .select('name email')
    .limit(10);

    res.json(users);
  } catch (err) {
    res.status(500).json({ message: 'Error searching users' });
  }
});

// Get user by ID
router.get('/users/:id', authenticateToken, allowRoles('doctor', 'admin'), async (req, res) => {
  try {
    const user = await User.findById(req.params.id).select('name email');
    if (!user) {
      return res.status(404).json({ message: 'User not found' });
    }
    res.json(user);
  } catch (err) {
    res.status(500).json({ message: 'Error fetching user' });
  }
});

module.exports = router;