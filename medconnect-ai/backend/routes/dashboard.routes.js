const express = require('express');
const { authenticateToken } = require('../middleware/auth');
const allowRoles = require('../middleware/role');
const User = require('../models/User');
const Appointment = require('../models/Appointment');

const router = express.Router();

// Patient Dashboard
router.get('/patient/dashboard', authenticateToken, allowRoles('patient'), async (req, res) => {
  try {
    console.log('Patient dashboard request from user:', req.user);
    const userId = req.user._id || req.user.id;
    console.log('Looking for user with ID:', userId);

    const user = await User.findById(userId);
    if (!user) {
      console.log('User not found with ID:', userId);
      return res.status(404).json({ message: 'User not found' });
    }
    console.log('Found user:', user.name);

    const appointments = await Appointment.find({ patientId: userId })
      .populate('doctorId', 'name')
      .sort({ date: 1, time: 1 });
    console.log('Found appointments:', appointments.length);

    const today = new Date();
    const upcomingAppointments = appointments.filter(apt =>
      new Date(apt.date) >= today && apt.status !== 'cancelled'
    );
    const pastAppointments = appointments.filter(apt =>
      new Date(apt.date) < today || apt.status === 'completed'
    );

    const response = {
      profile: {
        name: user.name,
        email: user.email,
        phone: user.phone,
        dateOfBirth: user.dateOfBirth,
        medicalHistory: user.medicalHistory || []
      },
      appointments,
      upcomingAppointments,
      pastAppointments
    };

    console.log('Sending patient dashboard response');
    res.json(response);
  } catch (error) {
    console.error('Patient dashboard error:', error);
    res.status(500).json({ message: 'Error fetching patient dashboard', error: error.message });
  }
});

// Doctor Dashboard
router.get('/doctor/dashboard', authenticateToken, allowRoles('doctor'), async (req, res) => {
  try {
    const userId = req.user._id;
    const user = await User.findById(userId);

    const appointments = await Appointment.find({ doctorId: userId })
      .populate('patientId', 'name')
      .sort({ date: 1, time: 1 });

    const today = new Date().toISOString().split('T')[0];
    const todayAppointments = appointments.filter(apt =>
      apt.date === today && apt.status === 'approved'
    );
    const upcomingAppointments = appointments.filter(apt =>
      apt.date > today && (apt.status === 'approved' || apt.status === 'pending')
    );

    const statistics = {
      totalPatients: await Appointment.distinct('patientId', { doctorId: userId }).length,
      appointmentsToday: todayAppointments.length,
      appointmentsThisWeek: appointments.filter(apt =>
        apt.date >= today &&
        apt.date <= new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString().split('T')[0]
      ).length,
      completedAppointments: appointments.filter(apt => apt.status === 'approved').length
    };

    res.json({
      profile: {
        name: user.name,
        email: user.email,
        phone: user.phone,
        specialization: user.specialization || 'Not specified',
        experience: user.experience || 0,
        qualifications: user.qualifications || []
      },
      statistics,
      todayAppointments,
      upcomingAppointments
    });
  } catch (error) {
    console.error('Doctor dashboard error:', error);
    res.status(500).json({ message: 'Error fetching doctor dashboard' });
  }
});

// Admin Dashboard
router.get('/admin/dashboard', authenticateToken, allowRoles('admin'), async (req, res) => {
  try {
    const users = await User.find().select('-password');
    const appointments = await Appointment.find();

    const statistics = {
      totalUsers: users.length,
      totalPatients: users.filter(user => user.role === 'patient').length,
      totalDoctors: users.filter(user => user.role === 'doctor').length,
      activeUsers: users.filter(user => user.status === 'active').length,
      pendingUsers: users.filter(user => user.status === 'pending').length,
      newUsersThisWeek: users.filter(user =>
        new Date(user.createdAt) >= new Date(Date.now() - 7 * 24 * 60 * 60 * 1000)
      ).length
    };

    const systemStats = {
      totalAppointments: appointments.length,
      completedAppointments: appointments.filter(apt => apt.status === 'completed').length,
      cancelledAppointments: appointments.filter(apt => apt.status === 'cancelled').length,
      averageAppointmentsPerDay: appointments.length / 30 // Assuming last 30 days
    };

    res.json({
      users,
      statistics,
      systemStats
    });
  } catch (error) {
    res.status(500).json({ message: 'Error fetching admin dashboard' });
  }
});

// Update user status (Admin only)
router.patch('/admin/users/:userId/status', authenticateToken, allowRoles('admin'), async (req, res) => {
  try {
    const { userId } = req.params;
    const { status } = req.body;

    const user = await User.findByIdAndUpdate(
      userId,
      { status },
      { new: true }
    ).select('-password');

    if (!user) {
      return res.status(404).json({ message: 'User not found' });
    }

    res.json(user);
  } catch (error) {
    res.status(500).json({ message: 'Error updating user status' });
  }
});

module.exports = router;