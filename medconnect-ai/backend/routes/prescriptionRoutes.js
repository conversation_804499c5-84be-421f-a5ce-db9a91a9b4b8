const express = require("express");
const router = express.Router();
const { authenticateToken } = require("../middleware/auth");
const allowRoles = require("../middleware/role");
const {
  addPrescription,
  getPatientPrescriptions,
  getDoctorPrescriptions
} = require("../controllers/prescriptionController");

router.post("/add", authenticateToken, allowRoles("doctor"), addPrescription);
router.get("/patient", authenticateToken, allowRoles("patient"), getPatientPrescriptions);
router.get("/doctor", authenticateToken, allowRoles("doctor"), getDoctorPrescriptions);

module.exports = router; 