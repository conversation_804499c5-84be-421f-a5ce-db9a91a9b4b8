{"version": 3, "file": "server_description.js", "sourceRoot": "", "sources": ["../../src/sdam/server_description.ts"], "names": [], "mappings": ";;;AAyMA,0CAgCC;AA6BD,wDA4BC;AAlSD,kCAA6D;AAC7D,oCAA8D;AAC9D,oCAAiG;AACjG,qCAAwD;AAExD,MAAM,qBAAqB,GAAG,IAAI,GAAG,CAAa;IAChD,mBAAU,CAAC,SAAS;IACpB,mBAAU,CAAC,UAAU;IACrB,mBAAU,CAAC,MAAM;IACjB,mBAAU,CAAC,YAAY;CACxB,CAAC,CAAC;AAEH,MAAM,yBAAyB,GAAG,IAAI,GAAG,CAAa;IACpD,mBAAU,CAAC,SAAS;IACpB,mBAAU,CAAC,WAAW;IACtB,mBAAU,CAAC,MAAM;IACjB,mBAAU,CAAC,UAAU;IACrB,mBAAU,CAAC,YAAY;CACxB,CAAC,CAAC;AAyBH;;;;;GAKG;AACH,MAAa,iBAAiB;IAkC5B;;;;;;OAMG;IACH,YACE,OAA6B,EAC7B,KAAgB,EAChB,UAAoC,EAAE;QAEtC,IAAI,OAAO,IAAI,IAAI,IAAI,OAAO,KAAK,EAAE,EAAE,CAAC;YACtC,MAAM,IAAI,yBAAiB,CAAC,6DAA6D,CAAC,CAAC;QAC7F,CAAC;QAED,IAAI,CAAC,OAAO;YACV,OAAO,OAAO,KAAK,QAAQ;gBACzB,CAAC,CAAC,mBAAW,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC,QAAQ,EAAE,CAAC,+BAA+B;gBAC5E,CAAC,CAAC,OAAO,CAAC,QAAQ,EAAE,CAAC;QACzB,IAAI,CAAC,IAAI,GAAG,eAAe,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;QAC5C,IAAI,CAAC,KAAK,GAAG,KAAK,EAAE,KAAK,EAAE,GAAG,CAAC,CAAC,IAAY,EAAE,EAAE,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC,IAAI,EAAE,CAAC;QAC3E,IAAI,CAAC,QAAQ,GAAG,KAAK,EAAE,QAAQ,EAAE,GAAG,CAAC,CAAC,IAAY,EAAE,EAAE,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC,IAAI,EAAE,CAAC;QACjF,IAAI,CAAC,QAAQ,GAAG,KAAK,EAAE,QAAQ,EAAE,GAAG,CAAC,CAAC,IAAY,EAAE,EAAE,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC,IAAI,EAAE,CAAC;QACjF,IAAI,CAAC,IAAI,GAAG,KAAK,EAAE,IAAI,IAAI,EAAE,CAAC;QAC9B,IAAI,CAAC,cAAc,GAAG,KAAK,EAAE,cAAc,IAAI,CAAC,CAAC;QACjD,IAAI,CAAC,cAAc,GAAG,KAAK,EAAE,cAAc,IAAI,CAAC,CAAC;QACjD,IAAI,CAAC,aAAa,GAAG,OAAO,EAAE,aAAa,IAAI,CAAC,CAAC,CAAC;QAClD,IAAI,CAAC,gBAAgB,GAAG,OAAO,EAAE,gBAAgB,IAAI,CAAC,CAAC;QACvD,IAAI,CAAC,cAAc,GAAG,IAAA,WAAG,GAAE,CAAC;QAC5B,IAAI,CAAC,aAAa,GAAG,KAAK,EAAE,SAAS,EAAE,aAAa,IAAI,CAAC,CAAC;QAC1D,6FAA6F;QAC7F,gEAAgE;QAChE,IAAI,CAAC,KAAK,GAAG,OAAO,CAAC,KAAK,IAAI,IAAI,CAAC;QACnC,IAAI,CAAC,KAAK,EAAE,KAAK,CAAC;QAClB,oDAAoD;QACpD,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,KAAK,EAAE,eAAe,IAAI,KAAK,EAAE,eAAe,IAAI,IAAI,CAAC;QACrF,IAAI,CAAC,OAAO,GAAG,KAAK,EAAE,OAAO,IAAI,IAAI,CAAC;QACtC,IAAI,CAAC,UAAU,GAAG,KAAK,EAAE,UAAU,IAAI,IAAI,CAAC;QAC5C,IAAI,CAAC,UAAU,GAAG,KAAK,EAAE,UAAU,IAAI,IAAI,CAAC;QAC5C,IAAI,CAAC,4BAA4B,GAAG,KAAK,EAAE,4BAA4B,IAAI,IAAI,CAAC;QAChF,IAAI,CAAC,mBAAmB,GAAG,KAAK,EAAE,mBAAmB,IAAI,IAAI,CAAC;QAC9D,IAAI,CAAC,iBAAiB,GAAG,KAAK,EAAE,iBAAiB,IAAI,IAAI,CAAC;QAC1D,IAAI,CAAC,iBAAiB,GAAG,KAAK,EAAE,iBAAiB,IAAI,IAAI,CAAC;QAC1D,IAAI,CAAC,OAAO,GAAG,KAAK,EAAE,OAAO,IAAI,IAAI,CAAC;QACtC,IAAI,CAAC,EAAE,GAAG,KAAK,EAAE,EAAE,EAAE,WAAW,EAAE,IAAI,IAAI,CAAC;QAC3C,IAAI,CAAC,YAAY,GAAG,KAAK,EAAE,YAAY,IAAI,IAAI,CAAC;QAChD,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC,KAAK,EAAE,QAAQ,CAAC,CAAC;IAC3C,CAAC;IAED,IAAI,WAAW;QACb,OAAO,mBAAW,CAAC,UAAU,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;IAC9C,CAAC;IAED,IAAI,QAAQ;QACV,OAAO,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;IAChE,CAAC;IAED,wCAAwC;IACxC,IAAI,UAAU;QACZ,OAAO,IAAI,CAAC,IAAI,KAAK,mBAAU,CAAC,WAAW,IAAI,IAAI,CAAC,UAAU,CAAC;IACjE,CAAC;IAED,kCAAkC;IAClC,IAAI,aAAa;QACf,OAAO,yBAAyB,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IAClD,CAAC;IAED,0CAA0C;IAC1C,IAAI,UAAU;QACZ,OAAO,qBAAqB,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IAC9C,CAAC;IAED,IAAI,IAAI;QACN,MAAM,UAAU,GAAG,IAAI,IAAI,CAAC,IAAI,EAAE,CAAC,MAAM,CAAC;QAC1C,OAAO,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,UAAU,CAAC,CAAC;IAC5C,CAAC;IAED,IAAI,IAAI;QACN,MAAM,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,CAAC;QAC3C,OAAO,IAAI,CAAC,CAAC,CAAC,MAAM,CAAC,QAAQ,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC;IAClD,CAAC;IAED;;;OAGG;IACH,MAAM,CAAC,KAAgC;QACrC,8FAA8F;QAC9F,wEAAwE;QACxE,MAAM,qBAAqB,GACzB,IAAI,CAAC,eAAe,KAAK,KAAK,EAAE,eAAe;YAC/C,sBAAsB,CAAC,IAAI,CAAC,eAAe,EAAE,KAAK,EAAE,eAAe,CAAC,KAAK,CAAC,CAAC;QAE7E,MAAM,gBAAgB,GACpB,IAAI,CAAC,UAAU,IAAI,IAAI,IAAI,KAAK,EAAE,UAAU,IAAI,IAAI;YAClD,CAAC,CAAC,IAAA,uBAAe,EAAC,IAAI,CAAC,UAAU,EAAE,KAAK,CAAC,UAAU,CAAC,KAAK,CAAC;YAC1D,CAAC,CAAC,IAAI,CAAC,UAAU,KAAK,KAAK,EAAE,UAAU,CAAC;QAE5C,OAAO,CACL,KAAK,IAAI,IAAI;YACb,KAAK,CAAC,QAAQ,KAAK,IAAI,CAAC,QAAQ;YAChC,IAAA,wBAAgB,EAAC,IAAI,CAAC,KAAK,EAAE,KAAK,CAAC,KAAK,CAAC;YACzC,IAAI,CAAC,IAAI,KAAK,KAAK,CAAC,IAAI;YACxB,IAAI,CAAC,cAAc,KAAK,KAAK,CAAC,cAAc;YAC5C,IAAA,wBAAgB,EAAC,IAAI,CAAC,KAAK,EAAE,KAAK,CAAC,KAAK,CAAC;YACzC,eAAe,CAAC,IAAI,CAAC,IAAI,EAAE,KAAK,CAAC,IAAI,CAAC;YACtC,IAAI,CAAC,OAAO,KAAK,KAAK,CAAC,OAAO;YAC9B,IAAI,CAAC,UAAU,KAAK,KAAK,CAAC,UAAU;YACpC,gBAAgB;YAChB,IAAI,CAAC,OAAO,KAAK,KAAK,CAAC,OAAO;YAC9B,IAAI,CAAC,4BAA4B,KAAK,KAAK,CAAC,4BAA4B;YACxE,qBAAqB,CACtB,CAAC;IACJ,CAAC;CACF;AArJD,8CAqJC;AAED,0DAA0D;AAC1D,SAAgB,eAAe,CAAC,KAAgB,EAAE,OAAkC;IAClF,IAAI,OAAO,EAAE,YAAY,EAAE,CAAC;QAC1B,OAAO,mBAAU,CAAC,YAAY,CAAC;IACjC,CAAC;IAED,IAAI,CAAC,KAAK,IAAI,CAAC,KAAK,CAAC,EAAE,EAAE,CAAC;QACxB,OAAO,mBAAU,CAAC,OAAO,CAAC;IAC5B,CAAC;IAED,IAAI,KAAK,CAAC,YAAY,EAAE,CAAC;QACvB,OAAO,mBAAU,CAAC,OAAO,CAAC;IAC5B,CAAC;IAED,IAAI,KAAK,CAAC,GAAG,IAAI,KAAK,CAAC,GAAG,KAAK,UAAU,EAAE,CAAC;QAC1C,OAAO,mBAAU,CAAC,MAAM,CAAC;IAC3B,CAAC;IAED,IAAI,KAAK,CAAC,OAAO,EAAE,CAAC;QAClB,IAAI,KAAK,CAAC,MAAM,EAAE,CAAC;YACjB,OAAO,mBAAU,CAAC,OAAO,CAAC;QAC5B,CAAC;aAAM,IAAI,KAAK,CAAC,iBAAiB,EAAE,CAAC;YACnC,OAAO,mBAAU,CAAC,SAAS,CAAC;QAC9B,CAAC;aAAM,IAAI,KAAK,CAAC,SAAS,EAAE,CAAC;YAC3B,OAAO,mBAAU,CAAC,WAAW,CAAC;QAChC,CAAC;aAAM,IAAI,KAAK,CAAC,WAAW,EAAE,CAAC;YAC7B,OAAO,mBAAU,CAAC,SAAS,CAAC;QAC9B,CAAC;aAAM,CAAC;YACN,OAAO,mBAAU,CAAC,OAAO,CAAC;QAC5B,CAAC;IACH,CAAC;IAED,OAAO,mBAAU,CAAC,UAAU,CAAC;AAC/B,CAAC;AAED,SAAS,eAAe,CAAC,IAAY,EAAE,KAAa;IAClD,MAAM,QAAQ,GAAG,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IACnC,MAAM,SAAS,GAAG,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;IAErC,OAAO,CACL,QAAQ,CAAC,MAAM,KAAK,SAAS,CAAC,MAAM;QACpC,QAAQ,CAAC,KAAK,CAAC,CAAC,GAAW,EAAE,EAAE,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,IAAI,CAAC,GAAG,CAAC,CAAC,CAC1D,CAAC;AACJ,CAAC;AAED;;;;;;;;;;;;;;;;GAgBG;AACH,SAAgB,sBAAsB,CACpC,SAAkC,EAClC,KAA8B;IAE9B,IAAI,SAAS,IAAI,IAAI,IAAI,KAAK,IAAI,IAAI,EAAE,CAAC;QACvC,OAAO,CAAC,CAAC,CAAC;IACZ,CAAC;IAED,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,MAAM,CAAC,KAAK,CAAC,SAAS,CAAC,EAAE,CAAC;QACjD,OAAO,CAAC,CAAC,CAAC;IACZ,CAAC;IAED,oDAAoD;IACpD,MAAM,cAAc,GAClB,OAAO,SAAS,CAAC,OAAO,KAAK,QAAQ;QACnC,CAAC,CAAC,WAAI,CAAC,UAAU,CAAC,SAAS,CAAC,OAAO,CAAC;QACpC,CAAC,CAAC,WAAI,CAAC,MAAM,CAAC,SAAS,CAAC,OAAO,CAAC;YAC9B,CAAC,CAAC,SAAS,CAAC,OAAO;YACnB,CAAC,CAAC,WAAI,CAAC,UAAU,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC;IAE3C,MAAM,UAAU,GACd,OAAO,KAAK,CAAC,OAAO,KAAK,QAAQ;QAC/B,CAAC,CAAC,WAAI,CAAC,UAAU,CAAC,KAAK,CAAC,OAAO,CAAC;QAChC,CAAC,CAAC,WAAI,CAAC,MAAM,CAAC,KAAK,CAAC,OAAO,CAAC;YAC1B,CAAC,CAAC,KAAK,CAAC,OAAO;YACf,CAAC,CAAC,WAAI,CAAC,UAAU,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;IAEvC,OAAO,cAAc,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC;AAC5C,CAAC"}