
import React, { useEffect } from 'react';
import { Routes, Route, Navigate } from 'react-router-dom';
import { gsap } from 'gsap';
import { ScrollTrigger } from 'gsap/ScrollTrigger';
import ProtectedRoute from './components/ProtectedRoute';
import Login from './pages/Login';
import Signup from './pages/Signup';
import Dashboard from './pages/Dashboard';
import LandingHeader from './components/LandingHeader';
import Hero from './components/Hero';
import Features from './components/Features';
import HowItWorks from './components/HowItWorks';
import Pricing from './components/Pricing';
import Footer from './components/Footer';
import ScrollToTop from './components/ScrollToTop';

import DoctorDashboard from './pages/DoctorDashboard';
import DoctorAppointments from './pages/DoctorAppointments';
import DoctorPatients from './pages/DoctorPatients';
import DoctorGeneratePrescription from './pages/DoctorGeneratePrescription';
import DoctorSettings from './pages/DoctorSettings';
import AdminDashboard from './pages/AdminDashboard';
import BookAppointment from './pages/BookAppointment';

import ViewPrescriptions from './pages/ViewPrescriptions';
import SymptomChecker from './pages/SymptomChecker';
import Navbar from './components/Navbar';
import MainLayout from './layouts/MainLayout';

// Placeholder dashboard components (you'll create these later)
const Unauthorized = () => <div>Unauthorized Access</div>;

// Landing Page Component
function LandingPage() {
  return (
    <>
      <LandingHeader />
      <main className="flex-grow">
        <Hero />
        <Features />
        <HowItWorks />
        <Pricing />
      </main>
      <Footer />
      <ScrollToTop />
    </>
  );
}

// Check if user is authenticated
const isAuthenticated = () => {
  return localStorage.getItem('token') !== null;
};

// Get user role
const getUserRole = () => {
  const user = localStorage.getItem('user');
  return user ? JSON.parse(user).role : null;
};

function App() {
  const userRole = getUserRole();
  const authenticated = isAuthenticated();

  useEffect(() => {
    // Register ScrollTrigger plugin
    gsap.registerPlugin(ScrollTrigger);

    // Set up animations for elements with the 'fade-in' class
    const fadeInElements = document.querySelectorAll('.fade-in');
    fadeInElements.forEach(element => {
      ScrollTrigger.create({
        trigger: element,
        start: 'top 80%',
        onEnter: () => element.classList.add('visible'),
      });
    });

    // Set up staggered animations for elements with the 'staggered-fade-in' class
    const staggeredContainers = document.querySelectorAll('.staggered-container');
    staggeredContainers.forEach(container => {
      const elements = container.querySelectorAll('.staggered-fade-in');

      ScrollTrigger.create({
        trigger: container,
        start: 'top 80%',
        onEnter: () => {
          gsap.to(elements, {
            opacity: 1,
            y: 0,
            stagger: 0.1,
            duration: 0.6,
            ease: 'power2.out',
          });
        },
      });
    });
  }, []);

  return (
    <div className="min-h-screen bg-gray-50">
        <Routes>
          {/* Public routes */}
          <Route path="/" element={<LandingPage />} />
          <Route path="/login" element={<Login />} />
          <Route path="/signup" element={<Signup />} />
          <Route path="/unauthorized" element={<Unauthorized />} />

          {/* Redirect to dashboard based on authentication and role */}
          <Route
            path="/app"
            element={
              authenticated ? (
                userRole === 'patient' ? <Navigate to="/dashboard" replace /> :
                userRole === 'doctor' ? <Navigate to="/doctor/dashboard" replace /> :
                userRole === 'admin' ? <Navigate to="/admin/dashboard" replace /> :
                <Navigate to="/dashboard" replace />
              ) : (
                <Navigate to="/login" replace />
              )
            }
          />

          {/* Patient Routes with New Layout */}
          <Route
            path="/dashboard"
            element={
              <ProtectedRoute requiredRole="patient">
                <MainLayout>
                  <Dashboard />
                </MainLayout>
              </ProtectedRoute>
            }
          />

          <Route
            path="/book-appointment"
            element={
              <ProtectedRoute requiredRole="patient">
                <MainLayout>
                  <BookAppointment />
                </MainLayout>
              </ProtectedRoute>
            }
          />

          <Route
            path="/appointments"
            element={
              <ProtectedRoute requiredRole="patient">
                <MainLayout>
                  <div className="py-4">
                    <h1 className="text-2xl font-bold text-gray-800 mb-6">My Appointments</h1>
                    <div className="bg-white shadow-sm border border-gray-200 rounded-lg p-6">
                      <p className="text-gray-600">Appointments page coming soon...</p>
                    </div>
                  </div>
                </MainLayout>
              </ProtectedRoute>
            }
          />

          <Route
            path="/prescriptions"
            element={
              <ProtectedRoute requiredRole="patient">
                <MainLayout>
                  <ViewPrescriptions />
                </MainLayout>
              </ProtectedRoute>
            }
          />

          <Route
            path="/symptom-checker"
            element={
              <ProtectedRoute requiredRole="patient">
                <MainLayout>
                  <SymptomChecker />
                </MainLayout>
              </ProtectedRoute>
            }
          />

          <Route
            path="/ai-consultation"
            element={
              <ProtectedRoute requiredRole="patient">
                <MainLayout>
                  <div className="py-4">
                    <h1 className="text-2xl font-bold text-gray-800 mb-6">AI Consultation</h1>
                    <div className="bg-white shadow-sm border border-gray-200 rounded-lg p-6">
                      <p className="text-gray-600">AI Consultation feature coming soon...</p>
                    </div>
                  </div>
                </MainLayout>
              </ProtectedRoute>
            }
          />

          <Route
            path="/settings"
            element={
              <ProtectedRoute requiredRole="patient">
                <MainLayout>
                  <div className="py-4">
                    <h1 className="text-2xl font-bold text-gray-800 mb-6">Settings</h1>
                    <div className="bg-white shadow-sm border border-gray-200 rounded-lg p-6">
                      <p className="text-gray-600">Settings page coming soon...</p>
                    </div>
                  </div>
                </MainLayout>
              </ProtectedRoute>
            }
          />

          {/* Doctor Routes with New Layout */}
          <Route
            path="/doctor/dashboard"
            element={
              <ProtectedRoute requiredRole="doctor">
                <DoctorDashboard />
              </ProtectedRoute>
            }
          />

          <Route
            path="/doctor/appointments"
            element={
              <ProtectedRoute requiredRole="doctor">
                <DoctorAppointments />
              </ProtectedRoute>
            }
          />

          <Route
            path="/doctor/patients"
            element={
              <ProtectedRoute requiredRole="doctor">
                <DoctorPatients />
              </ProtectedRoute>
            }
          />

          <Route
            path="/doctor/generate-prescription"
            element={
              <ProtectedRoute requiredRole="doctor">
                <DoctorGeneratePrescription />
              </ProtectedRoute>
            }
          />

          <Route
            path="/doctor/settings"
            element={
              <ProtectedRoute requiredRole="doctor">
                <DoctorSettings />
              </ProtectedRoute>
            }
          />

          {/* Legacy doctor prescription route for backward compatibility */}
          <Route
            path="/doctor/prescriptions/new"
            element={
              <ProtectedRoute requiredRole="doctor">
                <DoctorGeneratePrescription />
              </ProtectedRoute>
            }
          />

          <Route
            path="/admin/dashboard"
            element={
              <ProtectedRoute requiredRole="admin">
                <div className="min-h-screen bg-gray-50">
                  <Navbar />
                  <main className="container mx-auto px-4 py-8">
                    <AdminDashboard />
                  </main>
                </div>
              </ProtectedRoute>
            }
          />

          {/* Fallback route */}
          <Route path="*" element={<Navigate to="/" replace />} />
        </Routes>
      </div>
  );
}

export default App;