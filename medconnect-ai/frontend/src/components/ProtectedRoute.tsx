import { Navigate, useLocation } from 'react-router-dom';
import { ReactNode } from 'react';

interface ProtectedRouteProps {
  children: ReactNode;
  requiredRole: 'patient' | 'doctor' | 'admin';
}

export default function ProtectedRoute({ children, requiredRole }: ProtectedRouteProps) {
  const location = useLocation();
  const token = localStorage.getItem('token');
  const role = localStorage.getItem('role');

  if (!token) {
    return <Navigate to="/login" state={{ from: location }} replace />;
  }

  if (requiredRole && role !== requiredRole) {
    const dashboardPath = role === 'patient' ? '/dashboard'
      : role === 'doctor' ? '/doctor/dashboard'
      : '/admin/dashboard';
    return <Navigate to={dashboardPath} replace />;
  }

  return <>{children}</>;
}