import React, { useEffect, useRef } from 'react';
import { ArrowR<PERSON>, <PERSON>, Heart, Clock } from 'lucide-react';
import { useInView } from 'react-intersection-observer';
import gsap from 'gsap';
import { Link } from 'react-router-dom';

const Hero: React.FC = () => {
  const [ref, inView] = useInView({
    triggerOnce: true,
    threshold: 0.1,
  });

  const headingRef = useRef<HTMLHeadingElement>(null);
  const subheadingRef = useRef<HTMLParagraphElement>(null);
  const ctaRef = useRef<HTMLDivElement>(null);
  const statsRef = useRef<HTMLDivElement>(null);
  const imageRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (inView) {
      const tl = gsap.timeline();

      tl.fromTo(
        headingRef.current,
        { opacity: 0, y: 30, scale: 0.99 },
        { opacity: 1, y: 0, scale: 1, duration: 1, ease: 'power1.out' }
      )
      .fromTo(
        subheadingRef.current,
        { opacity: 0, y: 20, scale: 0.99 },
        { opacity: 1, y: 0, scale: 1, duration: 0.9, ease: 'power1.out' },
        '-=0.7'
      )
      .fromTo(
        ctaRef.current,
        { opacity: 0, y: 15 },
        { opacity: 1, y: 0, duration: 0.8, ease: 'power1.out' },
        '-=0.6'
      )
      .fromTo(
        statsRef.current,
        { opacity: 0, y: 10 },
        { opacity: 1, y: 0, duration: 0.7, ease: 'power1.out' },
        '-=0.5'
      )
      .fromTo(
        imageRef.current,
        { opacity: 0, x: 40, scale: 0.98 },
        { opacity: 1, x: 0, scale: 1, duration: 1, ease: 'power1.out' },
        '-=0.8'
      );
    }
  }, [inView]);

  return (
    <section ref={ref} className="min-h-screen pt-24 pb-16 md:pt-32 md:pb-24 bg-gradient-to-br from-white via-primary-50 to-secondary-50 relative overflow-hidden">
      {/* Background Video */}
      <div className="absolute inset-0 w-full h-full">
        <video
          autoPlay
          muted
          loop
          playsInline
          preload="auto"
          className="absolute inset-0 w-full h-full object-cover filter blur-sm opacity-70"
          style={{
            zIndex: -1,
            backgroundColor: 'transparent',
          }}
        >
          <source src="/neural.mp4" type="video/mp4" />
        </video>
        {/* Video overlay to maintain the gradient effect */}
        <div className="absolute inset-0 bg-gradient-to-br from-white/80 via-primary-50/70 to-secondary-50/70"></div>
      </div>

      <div className="container mx-auto px-4 relative z-10">
        <div className="flex flex-col lg:flex-row items-center justify-between gap-12">
          <div className="w-full lg:w-1/2 flex flex-col items-start">
            <h1
              ref={headingRef}
              className="text-3xl md:text-4xl lg:text-5xl font-bold text-gray-900 mb-8 md:mb-10 leading-tight max-w-3xl lg:max-w-2xl"
            >
              Digital Healthcare
              <span className="text-primary-500 ml-2">Reimagined with Intelligence.</span>
            </h1>
            <p
              ref={subheadingRef}
              className="text-lg md:text-xl text-gray-700 mb-10 md:mb-12 max-w-2xl lg:max-w-xl leading-relaxed"
            >
              Experience the future of healthcare with MedConnect AI. Our intelligent platform connects you with doctors, analyzes symptoms, and manages prescriptions - all powered by advanced AI.
            </p>

            <div ref={ctaRef} className="flex flex-col sm:flex-row gap-4 mb-10 w-full">
              <Link to="/login" className="btn btn-primary w-full sm:w-auto">
                Try For Free
                <ArrowRight className="ml-2 h-5 w-5" />
              </Link>
              <a href="#how-it-works" className="btn btn-outline w-full sm:w-auto">
                Schedule a Demo
              </a>
            </div>

            <div ref={statsRef} className="grid grid-cols-1 sm:grid-cols-3 gap-8 w-full">
              <div className="flex items-center">
                <div className="w-12 h-12 rounded-full bg-primary-100 flex items-center justify-center mr-4">
                  <Shield className="h-6 w-6 text-primary-500" />
                </div>
                <div>
                  <div className="text-xl font-semibold text-gray-900">HIPAA</div>
                  <div className="text-gray-600">Compliant</div>
                </div>
              </div>

              <div className="flex items-center">
                <div className="w-12 h-12 rounded-full bg-secondary-100 flex items-center justify-center mr-4">
                  <Heart className="h-6 w-6 text-secondary-500" />
                </div>
                <div>
                  <div className="text-xl font-semibold text-gray-900">98%</div>
                  <div className="text-gray-600">Satisfaction</div>
                </div>
              </div>

              <div className="flex items-center">
                <div className="w-12 h-12 rounded-full bg-primary-100 flex items-center justify-center mr-4">
                  <Clock className="h-6 w-6 text-primary-500" />
                </div>
                <div>
                  <div className="text-xl font-semibold text-gray-900">24/7</div>
                  <div className="text-gray-600">Support</div>
                </div>
              </div>
            </div>
          </div>

          <div ref={imageRef} className="w-full lg:w-1/2 flex justify-center lg:justify-end">
            <div className="relative w-full max-w-lg h-[500px]">
              {/* Background decorative elements */}
              <div className="absolute top-0 -left-4 w-72 h-72 bg-blue-300 rounded-full mix-blend-multiply filter blur-3xl opacity-20 animate-pulse-slow"></div>
              <div className="absolute top-0 -right-4 w-72 h-72 bg-blue-400 rounded-full mix-blend-multiply filter blur-3xl opacity-20 animate-pulse-slow"></div>

              {/* Medical illustration placeholder */}
              <div className="relative w-full h-full flex items-center justify-center">
                <img 
                  src="https://images.pexels.com/photos/7579831/pexels-photo-7579831.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=2" 
                  alt="Medical professional with technology"
                  className="w-full h-auto rounded-lg shadow-lg"
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default Hero;
