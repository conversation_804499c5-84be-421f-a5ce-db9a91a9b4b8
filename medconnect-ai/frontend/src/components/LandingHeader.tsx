import React, { useState, useEffect } from 'react';
import { Menu, X } from 'lucide-react';
import { Link } from 'react-router-dom';

const LandingHeader: React.FC = () => {
  const [isOpen, setIsOpen] = useState(false);
  const [isScrolled, setIsScrolled] = useState(false);

  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 10);
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  const toggleMenu = () => {
    setIsOpen(!isOpen);
  };

  return (
    <header
      className={`fixed w-full z-50 transition-all duration-300 ${
        isScrolled ? 'bg-white/95 backdrop-blur-sm shadow-soft py-3' : 'bg-transparent py-5'
      }`}
    >
      <div className="container mx-auto px-4 flex justify-between items-center">
        <a href="#" className="flex items-center">
          <span className="text-xl font-bold text-gray-900">MedConnectAI</span>
        </a>

        {/* Desktop Navigation */}
        <nav className="hidden md:flex items-center space-x-8">
          <a href="#features" className="text-gray-700 hover:text-gray-900 transition-colors font-medium">Features</a>
          <a href="#how-it-works" className="text-gray-700 hover:text-gray-900 transition-colors font-medium">How It Works</a>
          <a href="#pricing" className="text-gray-700 hover:text-gray-900 transition-colors font-medium">Pricing</a>
          <a href="#contact" className="text-gray-700 hover:text-gray-900 transition-colors font-medium">Contact</a>
          <Link to="/login" className="bg-purple-600 hover:bg-purple-700 text-white font-semibold py-2 px-6 rounded-lg transition-all duration-300 transform hover:scale-105">Get Started</Link>
        </nav>

        {/* Mobile Menu Button */}
        <button className="md:hidden text-gray-700" onClick={toggleMenu}>
          {isOpen ? <X size={24} /> : <Menu size={24} />}
        </button>
      </div>

      {/* Mobile Navigation */}
      <div className={`md:hidden transition-all duration-300 overflow-hidden ${isOpen ? 'max-h-96 opacity-100' : 'max-h-0 opacity-0'}`}>
        <div className="container mx-auto px-4 py-4 bg-white/95 backdrop-blur-sm">
          <div className="flex flex-col space-y-4">
            <a href="#features" className="text-gray-700 hover:text-gray-900 py-2 transition-colors font-medium" onClick={() => setIsOpen(false)}>Features</a>
            <a href="#how-it-works" className="text-gray-700 hover:text-gray-900 py-2 transition-colors font-medium" onClick={() => setIsOpen(false)}>How It Works</a>
            <a href="#pricing" className="text-gray-700 hover:text-gray-900 py-2 transition-colors font-medium" onClick={() => setIsOpen(false)}>Pricing</a>
            <a href="#contact" className="text-gray-700 hover:text-gray-900 py-2 transition-colors font-medium" onClick={() => setIsOpen(false)}>Contact</a>
            <Link to="/login" className="bg-purple-600 hover:bg-purple-700 text-white font-semibold py-2 px-6 rounded-lg transition-all duration-300 w-full text-center" onClick={() => setIsOpen(false)}>Get Started</Link>
          </div>
        </div>
      </div>
    </header>
  );
};

export default LandingHeader;
