import React, { useState, useEffect } from 'react';
import { Menu, X } from 'lucide-react';
import { Link } from 'react-router-dom';

const LandingHeader: React.FC = () => {
  const [isOpen, setIsOpen] = useState(false);
  const [isScrolled, setIsScrolled] = useState(false);

  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 10);
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  const toggleMenu = () => {
    setIsOpen(!isOpen);
  };

  return (
    <header 
      className={`fixed w-full z-50 transition-all duration-300 ${
        isScrolled ? 'bg-white shadow-soft py-3' : 'bg-transparent py-5'
      }`}
    >
      <div className="container mx-auto px-4 flex justify-between items-center">
        <a href="#" className="flex items-center">
          <div className="w-10 h-10 rounded-lg bg-primary-500 flex items-center justify-center mr-2">
            <div className="w-6 h-6 rounded-full bg-white"></div>
          </div>
          <span className="text-xl font-bold text-gray-900">MedConnect<span className="text-primary-500">AI</span></span>
        </a>
        
        {/* Desktop Navigation */}
        <nav className="hidden md:flex items-center space-x-8">
          <a href="#features" className="text-gray-700 hover:text-primary-500 transition-colors">Features</a>
          <a href="#how-it-works" className="text-gray-700 hover:text-primary-500 transition-colors">How It Works</a>
          <a href="#pricing" className="text-gray-700 hover:text-primary-500 transition-colors">Pricing</a>
          <a href="#contact" className="text-gray-700 hover:text-primary-500 transition-colors">Contact</a>
          <Link to="/login" className="btn btn-primary">Get Started</Link>
        </nav>
        
        {/* Mobile Menu Button */}
        <button className="md:hidden text-gray-700" onClick={toggleMenu}>
          {isOpen ? <X size={24} /> : <Menu size={24} />}
        </button>
      </div>
      
      {/* Mobile Navigation */}
      <div className={`md:hidden transition-all duration-300 overflow-hidden ${isOpen ? 'max-h-96 opacity-100' : 'max-h-0 opacity-0'}`}>
        <div className="container mx-auto px-4 py-4 bg-white">
          <div className="flex flex-col space-y-4">
            <a href="#features" className="text-gray-700 hover:text-primary-500 py-2 transition-colors" onClick={() => setIsOpen(false)}>Features</a>
            <a href="#how-it-works" className="text-gray-700 hover:text-primary-500 py-2 transition-colors" onClick={() => setIsOpen(false)}>How It Works</a>
            <a href="#pricing" className="text-gray-700 hover:text-primary-500 py-2 transition-colors" onClick={() => setIsOpen(false)}>Pricing</a>
            <a href="#contact" className="text-gray-700 hover:text-primary-500 py-2 transition-colors" onClick={() => setIsOpen(false)}>Contact</a>
            <Link to="/login" className="btn btn-primary w-full text-center" onClick={() => setIsOpen(false)}>Get Started</Link>
          </div>
        </div>
      </div>
    </header>
  );
};

export default LandingHeader;
