import React from 'react';
import { Link } from 'react-router-dom';
import { <PERSON>, Alert<PERSON>riangle, ChevronRight } from 'lucide-react';

interface AIInsightsPanelProps {
  lastSymptomCheck: {
    date: string;
    symptoms: string[];
    likelyDiagnosis: string;
    severity: 'low' | 'medium' | 'high';
    recommendation: string;
  } | null;
}

const AIInsightsPanel: React.FC<AIInsightsPanelProps> = ({ lastSymptomCheck }) => {
  const getSeverityColor = (severity: 'low' | 'medium' | 'high') => {
    switch (severity) {
      case 'low':
        return 'text-green-700 bg-green-50 border-green-200';
      case 'medium':
        return 'text-yellow-700 bg-yellow-50 border-yellow-200';
      case 'high':
        return 'text-red-700 bg-red-50 border-red-200';
      default:
        return 'text-gray-700 bg-gray-50 border-gray-200';
    }
  };
  
  return (
    <div className="mb-8">
      <h2 className="text-xl font-semibold text-gray-800 mb-4">AI Insights</h2>
      
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
        {lastSymptomCheck ? (
          <div className="p-4">
            <div className="flex items-center mb-3">
              <div className="bg-blue-100 rounded-full p-2 mr-3">
                <Brain className="h-5 w-5 text-blue-600" />
              </div>
              <div>
                <h3 className="font-medium text-gray-900">Last Symptom Check</h3>
                <p className="text-sm text-gray-500">{lastSymptomCheck.date}</p>
              </div>
            </div>
            
            <div className="mb-3">
              <p className="text-sm text-gray-600 mb-1">Reported symptoms:</p>
              <div className="flex flex-wrap gap-2">
                {lastSymptomCheck.symptoms.map((symptom, index) => (
                  <span 
                    key={index}
                    className="inline-block bg-gray-100 text-gray-700 text-xs rounded-full px-2 py-1"
                  >
                    {symptom}
                  </span>
                ))}
              </div>
            </div>
            
            <div className="mb-3">
              <p className="text-sm text-gray-600 mb-1">Likely diagnosis:</p>
              <div className="flex items-center">
                <span className={`text-sm font-medium rounded-md px-2 py-1 border ${getSeverityColor(lastSymptomCheck.severity)}`}>
                  {lastSymptomCheck.likelyDiagnosis}
                </span>
                <span className={`ml-2 text-xs rounded-full px-2 py-0.5 ${getSeverityColor(lastSymptomCheck.severity)}`}>
                  {lastSymptomCheck.severity.charAt(0).toUpperCase() + lastSymptomCheck.severity.slice(1)} severity
                </span>
              </div>
            </div>
            
            <div className="mb-3 text-sm text-gray-600">
              <p className="font-medium">Recommendation:</p>
              <p>{lastSymptomCheck.recommendation}</p>
            </div>
            
            <div className="flex space-x-3 mt-4">
              <Link 
                to="/symptom-checker" 
                className="px-3 py-1.5 bg-blue-600 text-white rounded-md text-sm font-medium hover:bg-blue-700 transition-colors duration-150"
              >
                New Symptom Check
              </Link>
              <Link 
                to="/ai-consultation" 
                className="px-3 py-1.5 bg-white border border-gray-300 text-gray-700 rounded-md text-sm font-medium hover:bg-gray-50 transition-colors duration-150"
              >
                AI Consultation Chat
              </Link>
            </div>
          </div>
        ) : (
          <div className="p-4">
            <div className="flex items-center mb-3">
              <div className="bg-yellow-100 rounded-full p-2 mr-3">
                <AlertTriangle className="h-5 w-5 text-yellow-600" />
              </div>
              <h3 className="font-medium text-gray-900">No recent symptom checks</h3>
            </div>
            <p className="text-sm text-gray-600 mb-4">
              Our AI-powered symptom checker can help you understand your symptoms and provide health guidance.
            </p>
            <Link 
              to="/symptom-checker" 
              className="px-3 py-1.5 bg-blue-600 text-white rounded-md text-sm font-medium hover:bg-blue-700 transition-colors duration-150 inline-block"
            >
              Start Symptom Check
            </Link>
          </div>
        )}
      </div>
      
      <div className="mt-4 bg-blue-50 border border-blue-200 rounded-lg p-4">
        <h3 className="font-medium text-blue-800 mb-2 flex items-center">
          <svg className="h-5 w-5 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
          Health Tips
        </h3>
        <p className="text-sm text-blue-700">
          Remember to stay hydrated and maintain a balanced diet rich in fruits and vegetables. Regular exercise can help boost your immune system.
        </p>
        <Link 
          to="/health-tips" 
          className="mt-2 text-sm text-blue-600 hover:text-blue-800 flex items-center"
        >
          More health tips <ChevronRight className="h-4 w-4 ml-1" />
        </Link>
      </div>
    </div>
  );
};

export default AIInsightsPanel;
