import React, { useEffect, useRef } from 'react';
import { gsap } from 'gsap';
import { Heart } from 'lucide-react';

const MedicalIllustration: React.FC = () => {
  const illustrationRef = useRef<HTMLDivElement>(null);
  
  useEffect(() => {
    if (illustrationRef.current) {
      // Animate illustration elements
      gsap.fromTo(
        illustrationRef.current,
        { 
          opacity: 0,
          scale: 0.8
        },
        { 
          opacity: 1,
          scale: 1,
          duration: 1,
          ease: 'back.out(1.7)'
        }
      );
    }
  }, []);

  return (
    <div ref={illustrationRef} className="flex items-center justify-center">
      <Heart className="h-16 w-16 text-white/90" />
    </div>
  );
};

export default MedicalIllustration;
