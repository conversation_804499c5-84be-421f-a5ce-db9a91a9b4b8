import React, { useState } from 'react';
import { Bell, ChevronDown, LogOut, Settings, User } from 'lucide-react';
import { useNavigate } from 'react-router-dom';
import Badge from '../ui/Badge';

interface Notification {
  id: string;
  message: string;
  time: string;
  read: boolean;
  type: 'appointment' | 'patient' | 'prescription' | 'system';
}

// Mock notifications - in real app, this would come from API
const mockNotifications: Notification[] = [
  {
    id: 'n1',
    message: 'New appointment request received',
    time: '10 minutes ago',
    read: false,
    type: 'appointment',
  },
  {
    id: 'n2',
    message: 'Lab results available for patient',
    time: '1 hour ago',
    read: false,
    type: 'patient',
  },
  {
    id: 'n3',
    message: 'Prescription refill request',
    time: '3 hours ago',
    read: true,
    type: 'prescription',
  },
];

interface DoctorHeaderProps {
  doctorName?: string;
}

const DoctorHeader: React.FC<DoctorHeaderProps> = ({ doctorName = 'Doctor' }) => {
  const [showNotifications, setShowNotifications] = useState(false);
  const [showProfileMenu, setShowProfileMenu] = useState(false);
  const navigate = useNavigate();
  
  const unreadNotifications = mockNotifications.filter(n => !n.read).length;
  
  const handleLogout = () => {
    localStorage.removeItem('token');
    localStorage.removeItem('userRole');
    navigate('/login');
  };
  
  return (
    <header className="bg-white border-b border-gray-200 h-16 flex items-center justify-between px-6 shadow-sm">
      <div className="flex-1">
        <h2 className="text-xl font-semibold text-gray-800">Welcome, Dr. {doctorName}</h2>
      </div>
      
      <div className="flex items-center space-x-4">
        {/* Notifications */}
        <div className="relative">
          <button 
            className="p-2 rounded-full text-gray-600 hover:bg-gray-100 relative"
            onClick={() => {
              setShowNotifications(!showNotifications);
              setShowProfileMenu(false);
            }}
          >
            <Bell size={20} />
            {unreadNotifications > 0 && (
              <span className="absolute top-0 right-0 block h-4 w-4 rounded-full bg-[#2563EB] text-white text-xs flex items-center justify-center">
                {unreadNotifications}
              </span>
            )}
          </button>
          
          {showNotifications && (
            <div className="absolute right-0 mt-2 w-80 bg-white rounded-lg shadow-lg py-1 z-10 border border-gray-200">
              <div className="px-4 py-2 border-b border-gray-100">
                <p className="text-sm font-medium text-gray-700">Notifications</p>
              </div>
              <div className="max-h-96 overflow-y-auto">
                {mockNotifications.map((notification) => (
                  <div
                    key={notification.id}
                    className={`px-4 py-3 border-b border-gray-100 hover:bg-gray-50 ${
                      !notification.read ? 'bg-blue-50' : ''
                    }`}
                  >
                    <div className="flex justify-between items-start">
                      <p className="text-sm text-gray-800">{notification.message}</p>
                      <Badge
                        variant={
                          notification.type === 'appointment'
                            ? 'info'
                            : notification.type === 'patient'
                            ? 'success'
                            : 'warning'
                        }
                        className="ml-2"
                      >
                        {notification.type}
                      </Badge>
                    </div>
                    <p className="text-xs text-gray-500 mt-1">{notification.time}</p>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
        
        {/* Profile dropdown */}
        <div className="relative">
          <button
            className="flex items-center text-gray-700 hover:text-[#2563EB]"
            onClick={() => {
              setShowProfileMenu(!showProfileMenu);
              setShowNotifications(false);
            }}
          >
            <div className="w-8 h-8 rounded-full bg-blue-600 flex items-center justify-center text-white text-sm font-medium mr-2">
              {doctorName.charAt(0).toUpperCase()}
            </div>
            <span className="text-sm font-medium mr-1">Dr. {doctorName}</span>
            <ChevronDown size={16} />
          </button>
          
          {showProfileMenu && (
            <div className="absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-lg py-1 z-10 border border-gray-200">
              <button className="flex w-full items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                <User size={16} className="mr-2" />
                View Profile
              </button>
              <button className="flex w-full items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                <Settings size={16} className="mr-2" />
                Account Settings
              </button>
              <div className="border-t border-gray-100 my-1"></div>
              <button 
                onClick={handleLogout}
                className="flex w-full items-center px-4 py-2 text-sm text-red-600 hover:bg-gray-100"
              >
                <LogOut size={16} className="mr-2" />
                Sign Out
              </button>
            </div>
          )}
        </div>
      </div>
    </header>
  );
};

export default DoctorHeader;
