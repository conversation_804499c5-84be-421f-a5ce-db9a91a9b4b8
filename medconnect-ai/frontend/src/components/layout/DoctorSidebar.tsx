import React from 'react';
import { Link, useLocation } from 'react-router-dom';
import {
  LayoutDashboard,
  Calendar,
  Users,
  FileText,
  Settings,
  Stethoscope
} from 'lucide-react';

interface DoctorSidebarProps {
  doctorName?: string;
}

const DoctorSidebar: React.FC<DoctorSidebarProps> = ({ doctorName = 'Doctor' }) => {
  const location = useLocation();

  const menuItems = [
    {
      path: '/doctor/dashboard',
      name: 'Dashboard',
      icon: <LayoutDashboard size={20} />
    },
    {
      path: '/doctor/appointments',
      name: 'Appointments',
      icon: <Calendar size={20} />
    },
    {
      path: '/doctor/patients',
      name: 'Patients',
      icon: <Users size={20} />
    },
    {
      path: '/doctor/generate-prescription',
      name: 'Generate Prescription',
      icon: <FileText size={20} />
    },
    {
      path: '/doctor/settings',
      name: 'Settings',
      icon: <Settings size={20} />
    }
  ];

  return (
    <div className="w-64 bg-[#2563EB] text-white flex flex-col h-screen">
      <div className="p-5">
        <div className="flex items-center gap-2">
          <Stethoscope className="h-8 w-8" />
          <div>
            <h1 className="text-xl font-bold">MedConnect AI</h1>
            <p className="text-xs text-blue-200">Doctor Dashboard</p>
          </div>
        </div>
      </div>

      <div className="flex-1 py-6">
        <nav className="px-3 space-y-1">
          {menuItems.map((item) => {
            const isActive = location.pathname === item.path;
            return (
              <Link
                key={item.path}
                to={item.path}
                className={`flex items-center px-3 py-3 text-sm font-medium rounded-md transition-colors ${
                  isActive
                    ? 'bg-blue-700 text-white'
                    : 'text-blue-100 hover:bg-blue-700'
                }`}
              >
                <span className="mr-3">
                  {item.icon}
                </span>
                {item.name}
              </Link>
            );
          })}
        </nav>
      </div>

      <div className="p-4 border-t border-blue-700">
        <div className="flex items-center">
          <div className="w-8 h-8 rounded-full bg-blue-700 flex items-center justify-center">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
              className="w-4 h-4"
            >
              <path d="M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2"></path>
              <circle cx="12" cy="7" r="4"></circle>
            </svg>
          </div>
          <div className="ml-3">
            <p className="text-xs text-blue-200">Logged in as</p>
            <p className="text-sm font-medium">Dr. {doctorName}</p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default DoctorSidebar;
