@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Poppins:wght@400;500;600;700;800&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  html {
    scroll-behavior: smooth;
  }

  body {
    font-family: 'Inter', sans-serif;
    line-height: 1.6;
    margin: 0;
    padding: 0;
  }

  h1, h2, h3, h4, h5, h6 {
    font-family: 'Poppins', sans-serif;
  }

  #root {
    min-height: 100vh;
  }
}

@layer components {
  .btn-primary {
    @apply bg-gradient-to-r from-purple-600 to-purple-700 hover:from-purple-700 hover:to-purple-800 text-white font-semibold py-4 px-8 rounded-xl transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-2xl;
  }

  .btn-secondary {
    @apply bg-transparent border-2 border-purple-600 text-purple-600 hover:bg-purple-600 hover:text-white font-semibold py-4 px-8 rounded-xl transition-all duration-300;
  }

  .section-padding {
    @apply py-20 md:py-32;
  }

  .container-custom {
    @apply max-w-7xl mx-auto px-4 sm:px-6 lg:px-8;
  }

  .card-hover {
    @apply transition-all duration-500 transform hover:scale-105 hover:shadow-2xl hover:-translate-y-2;
  }

  .text-gradient {
    @apply bg-gradient-to-r from-purple-600 via-purple-700 to-purple-800 bg-clip-text text-transparent;
  }

  .glass-card {
    @apply bg-white/10 backdrop-blur-md border border-white/20 rounded-2xl;
  }

  .medical-gradient {
    @apply bg-gradient-to-br from-purple-50 via-purple-100 to-purple-200;
  }

  .btn-outline {
    @apply bg-transparent border-2 border-gray-300 text-gray-700 hover:bg-gray-100 hover:border-gray-400 font-semibold py-4 px-8 rounded-xl transition-all duration-300;
  }

  .primary-50 {
    @apply bg-purple-50;
  }

  .secondary-50 {
    @apply bg-blue-50;
  }

  .primary-100 {
    @apply bg-purple-100;
  }

  .secondary-100 {
    @apply bg-blue-100;
  }

  .primary-500 {
    @apply text-purple-500;
  }

  .secondary-500 {
    @apply text-blue-500;
  }

  .btn {
    @apply inline-flex items-center justify-center font-semibold py-3 px-6 rounded-lg transition-all duration-300 transform hover:scale-105;
  }

  .animate-pulse-slow {
    animation: pulse 4s cubic-bezier(0.4, 0, 0.6, 1) infinite;
  }

  /* Auth Layout & Form Styles */
  .card {
    @apply bg-white rounded-2xl shadow-xl p-8 border border-gray-100;
  }

  .page-transition {
    @apply transition-all duration-500 ease-in-out;
  }

  .shadow-card {
    @apply shadow-2xl;
  }

  .link {
    @apply text-purple-600 hover:text-purple-700 font-medium transition-colors duration-200 hover:underline;
  }

  /* Form Input Styles */
  .form-label {
    @apply block text-sm font-medium text-slate-700 mb-2;
  }

  .form-input {
    @apply w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-purple-500 focus:border-purple-500 transition-all duration-200 bg-white text-slate-900 placeholder-slate-400;
  }

  /* Checkbox Styles */
  .checkbox-label {
    @apply ml-2 text-sm text-slate-700 cursor-pointer;
  }

  /* Color Variables for Components */
  .primary {
    @apply bg-purple-600;
  }

  .secondary {
    @apply bg-blue-600;
  }

  /* Form Element Animation */
  .form-element {
    @apply opacity-100 transform translate-y-0;
  }

  /* Prevent unwanted zoom effects on mobile */
  * {
    -webkit-tap-highlight-color: transparent;
    -webkit-touch-callout: none;
    -webkit-user-select: none;
    -khtml-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
  }

  /* Allow text selection for content */
  p, span, h1, h2, h3, h4, h5, h6, input, textarea {
    -webkit-user-select: text;
    -khtml-user-select: text;
    -moz-user-select: text;
    -ms-user-select: text;
    user-select: text;
  }
}

@layer utilities {
  .animation-delay-200 {
    animation-delay: 200ms;
  }

  .animation-delay-400 {
    animation-delay: 400ms;
  }

  .animation-delay-600 {
    animation-delay: 600ms;
  }
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
  background: #0066CC;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #0052A3;
}

/* Three.js canvas styling */
#three-canvas {
  position: fixed;
  top: 0;
  left: 0;
  z-index: -1;
  pointer-events: none;
}

/* Auth video background styling */
.auth-video-bg {
  filter: brightness(0.8) contrast(1.1) saturate(1.2);
  transition: filter 0.3s ease;
  animation: subtle-pulse 8s ease-in-out infinite;
}

.auth-video-bg:hover {
  filter: brightness(0.9) contrast(1.2) saturate(1.3);
}

/* Ensure video covers the container properly - Within card */
.auth-video-container {
  min-height: 600px;
  height: 100%;
}

/* Ensure card has proper height for video background and no gaps */
.shadow-card {
  min-height: 600px;
  display: flex;
}

/* Remove any potential gaps in video background */
.auth-video-bg {
  margin: 0;
  padding: 0;
  border: none;
  outline: none;
}

@media (max-width: 768px) {
  .auth-video-container {
    min-height: 400px;
  }

  .shadow-card {
    min-height: 400px;
  }
}

/* Subtle pulse animation for video background */
@keyframes subtle-pulse {
  0%, 100% {
    transform: scale(1);
    filter: brightness(0.8) contrast(1.1) saturate(1.2);
  }
  50% {
    transform: scale(1.02);
    filter: brightness(0.85) contrast(1.15) saturate(1.25);
  }
}

/* Glass morphism effect for content overlay */
.auth-content-glass {
  backdrop-filter: blur(10px);
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37);
}

@layer base {
  html {
    font-family: 'Inter', system-ui, sans-serif;
  }
  
  body {
    @apply bg-gray-50 text-gray-900;
  }
}

@layer components {
  .btn {
    @apply px-4 py-2 rounded-md font-medium transition-colors duration-200;
  }
  
  .btn-primary {
    @apply bg-primary-600 text-white hover:bg-primary-700 focus:ring-2 focus:ring-primary-500 focus:ring-offset-2;
  }
  
  .btn-secondary {
    @apply bg-secondary-200 text-secondary-800 hover:bg-secondary-300 focus:ring-2 focus:ring-secondary-500 focus:ring-offset-2;
  }
  
  .input {
    @apply w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500;
  }
  
  .label {
    @apply block text-sm font-medium text-gray-700 mb-1;
  }
}

:root {
  font-family: system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
}

body {
  margin: 0;
  min-height: 100vh;
}

#root {
  min-height: 100vh;
}
