import axios from 'axios';
import type { LoginFormData, SignupFormData, AuthResponse } from '../types/auth';

const API_URL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:5000';

const api = axios.create({
  baseURL: API_URL,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Add token to requests if it exists
api.interceptors.request.use((config) => {
  const token = localStorage.getItem('token');
  if (token) {
    config.headers.Authorization = `Bearer ${token}`;
  }
  return config;
});

export const authService = {
  async login(data: LoginFormData): Promise<AuthResponse> {
    try {
      const response = await api.post<AuthResponse>('/api/auth/login', data);
      return response.data;
    } catch (error) {
      // Mock authentication for demo purposes when backend is not available
      console.log('Backend not available, using mock authentication');

      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Mock successful login
      const mockResponse: AuthResponse = {
        token: 'mock-jwt-token-' + Date.now(),
        user: {
          id: '1',
          email: data.email,
          name: 'Demo User',
          role: 'patient'
        }
      };

      // Store user info in localStorage for demo
      localStorage.setItem('role', mockResponse.user.role);
      localStorage.setItem('name', mockResponse.user.name);

      return mockResponse;
    }
  },

  async signup(data: SignupFormData): Promise<{ message: string }> {
    try {
      const response = await api.post<{ message: string }>('/api/auth/signup', data);
      return response.data;
    } catch (error) {
      // Mock signup for demo purposes
      console.log('Backend not available, using mock signup');

      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 1000));

      return { message: 'Account created successfully! Please login.' };
    }
  },

  logout() {
    localStorage.removeItem('token');
    localStorage.removeItem('role');
    localStorage.removeItem('name');
  }
};

export default api; 