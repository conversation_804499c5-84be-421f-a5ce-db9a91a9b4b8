import React, { useState, useEffect, useRef } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { gsap } from 'gsap';
import { Mail, Lock, Eye, EyeOff } from 'lucide-react';
import FormInput from '../components/ui/LandingFormInput';
import AuthLayout from '../components/ui/LandingAuthLayout';
import Checkbox from '../components/ui/LandingCheckbox';
import AuthHeader from '../components/ui/LandingAuthHeader';
import FormButton from '../components/ui/LandingFormButton';
import SocialLogin from '../components/ui/LandingSocialLogin';
import { authService } from '../services/api';
import type { LoginFormData } from '../types/auth';

const Login: React.FC = () => {
  const navigate = useNavigate();
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [rememberMe, setRememberMe] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [errors, setErrors] = useState<{email?: string; password?: string}>({});
  const [error, setError] = useState<string>('');

  const formRef = useRef<HTMLFormElement>(null);

  useEffect(() => {
    // Animate form elements
    const tl = gsap.timeline();

    tl.fromTo(
      '.form-element',
      {
        opacity: 0,
        y: 20,
      },
      {
        opacity: 1,
        y: 0,
        stagger: 0.1,
        ease: 'power2.out',
      },
    );
  }, []);

  const validate = () => {
    const newErrors: {email?: string; password?: string} = {};
    let isValid = true;

    if (!email.trim()) {
      newErrors.email = 'Email is required';
      isValid = false;
    }

    if (!password) {
      newErrors.password = 'Password is required';
      isValid = false;
    }

    setErrors(newErrors);
    return isValid;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError('');

    if (!validate()) return;

    setIsLoading(true);

    try {
      const loginData: LoginFormData = { email, password };

      // Mock authentication for demo purposes
      console.log('Attempting login with:', loginData);

      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Mock successful login
      const mockResponse = {
        token: 'mock-jwt-token-' + Date.now(),
        user: {
          id: '1',
          email: email,
          name: 'Demo User',
          role: 'patient'
        }
      };

      // Store token and user info in localStorage
      localStorage.setItem('token', mockResponse.token);
      localStorage.setItem('role', mockResponse.user.role);
      localStorage.setItem('name', mockResponse.user.name);

      // Navigate to dashboard
      navigate('/dashboard');
    } catch (err: any) {
      setError('Login failed. Please try again.');
    } finally {
      setIsLoading(false);
    }

    // Animate button on submit
    gsap.to('.submit-btn', {
      scale: 0.95,
      duration: 0.1,
      yoyo: true,
      repeat: 1,
    });
  };

  const togglePasswordVisibility = () => {
    setShowPassword(!showPassword);
  };

  return (
    <AuthLayout>
      <div className="page-transition w-full max-w-md mx-auto">
        <form
          ref={formRef}
          onSubmit={handleSubmit}
          className="card"
        >
          <AuthHeader
            title="Login to MedConnect AI"
            subtitle="Enter your credentials to access your account"
          />

          {error && (
            <div className="rounded-md bg-red-50 p-4 mb-4">
              <div className="text-sm text-red-700">{error}</div>
            </div>
          )}

          <div className="space-y-5">
            <div className="form-element">
              <FormInput
                label="Email or Username"
                type="text"
                id="email"
                value={email}
                onChange={(e) => {
                  setEmail(e.target.value);
                  if (errors.email) setErrors({...errors, email: undefined});
                }}
                placeholder="Enter your email or username"
                icon={<Mail size={18} />}
                required
                error={errors.email}
              />
            </div>

            <div className="form-element">
              <FormInput
                label="Password"
                type={showPassword ? 'text' : 'password'}
                id="password"
                value={password}
                onChange={(e) => {
                  setPassword(e.target.value);
                  if (errors.password) setErrors({...errors, password: undefined});
                }}
                placeholder="Enter your password"
                icon={<Lock size={18} />}
                required
                error={errors.password}
                rightIcon={
                  showPassword ?
                    <Eye
                      size={18}
                      className="cursor-pointer text-slate-400 hover:text-slate-600"
                      onClick={togglePasswordVisibility}
                    /> :
                    <EyeOff
                      size={18}
                      className="cursor-pointer text-slate-400 hover:text-slate-600"
                      onClick={togglePasswordVisibility}
                    />
                }
              />
            </div>

            <div className="form-element flex items-center justify-between">
              <Checkbox
                id="remember-me"
                checked={rememberMe}
                onChange={() => setRememberMe(!rememberMe)}
                label="Remember me"
              />

              <a href="#" className="link text-sm">
                Forgot password?
              </a>
            </div>

            <div className="form-element">
              <FormButton
                type="submit"
                variant="primary"
                isLoading={isLoading}
              >
                Login
              </FormButton>
            </div>

            <SocialLogin />

            <p className="form-element text-center text-sm text-slate-600 mt-6">
              Don't have an account? {' '}
              <Link to="/signup" className="link">
                Sign Up
              </Link>
            </p>
          </div>
        </form>
      </div>
    </AuthLayout>
  );
};

export default Login;