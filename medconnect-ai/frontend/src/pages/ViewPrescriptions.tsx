import React, { useEffect, useState } from "react";
import axios from "axios";
import jsPDF from "jspdf";

interface Prescription {
  _id: string;
  diagnosis: string;
  medicines: string[];
  advice: string;
  createdAt: string;
  doctorId: {
    name: string;
  };
}

export default function ViewPrescriptions() {
  const [prescriptions, setPrescriptions] = useState<Prescription[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchPrescriptions = async () => {
      const token = localStorage.getItem("token");
      try {
        const res = await axios.get(
          `${import.meta.env.VITE_API_BASE_URL}/api/prescriptions/patient`,
          { headers: { Authorization: `Bearer ${token}` } }
        );
        setPrescriptions(res.data);
      } catch (err) {
        alert("Failed to load prescriptions");
      } finally {
        setLoading(false);
      }
    };
    fetchPrescriptions();
  }, []);

  const generatePDF = (prescription: Prescription) => {
    const doc = new jsPDF();
    const lineHeight = 10;
    let y = 20;

    // Add title
    doc.setFontSize(20);
    doc.text("Medical Prescription", 20, y);
    y += lineHeight * 2;

    // Add content
    doc.setFontSize(12);
    doc.text(`Doctor: ${prescription.doctorId.name}`, 20, y);
    y += lineHeight;
    doc.text(`Date: ${new Date(prescription.createdAt).toLocaleDateString()}`, 20, y);
    y += lineHeight * 1.5;

    doc.text("Diagnosis:", 20, y);
    y += lineHeight;
    doc.text(prescription.diagnosis, 30, y);
    y += lineHeight * 1.5;

    doc.text("Medicines:", 20, y);
    y += lineHeight;
    prescription.medicines.forEach(medicine => {
      doc.text(`• ${medicine}`, 30, y);
      y += lineHeight;
    });
    y += lineHeight * 0.5;

    if (prescription.advice) {
      doc.text("Advice:", 20, y);
      y += lineHeight;
      doc.text(prescription.advice, 30, y);
    }

    doc.save(`prescription-${prescription._id}.pdf`);
  };

  if (loading) {
    return <div className="text-center p-6">Loading prescriptions...</div>;
  }

  return (
    <div className="max-w-4xl mx-auto p-6">
      <h2 className="text-2xl font-bold mb-6">Your Prescriptions</h2>
      {prescriptions.length === 0 ? (
        <p className="text-gray-600">No prescriptions found.</p>
      ) : (
        <div className="space-y-4">
          {prescriptions.map((prescription) => (
            <div
              key={prescription._id}
              className="border rounded-lg p-4 shadow-sm hover:shadow-md transition-shadow"
            >
              <div className="flex justify-between items-start mb-4">
                <div>
                  <h3 className="font-semibold">Dr. {prescription.doctorId.name}</h3>
                  <p className="text-gray-600 text-sm">
                    {new Date(prescription.createdAt).toLocaleDateString()}
                  </p>
                </div>
                <button
                  onClick={() => generatePDF(prescription)}
                  className="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600"
                >
                  Download PDF
                </button>
              </div>
              <div className="space-y-2">
                <div>
                  <h4 className="font-medium">Diagnosis:</h4>
                  <p className="text-gray-700">{prescription.diagnosis}</p>
                </div>
                <div>
                  <h4 className="font-medium">Medicines:</h4>
                  <ul className="list-disc list-inside text-gray-700">
                    {prescription.medicines.map((medicine, index) => (
                      <li key={index}>{medicine}</li>
                    ))}
                  </ul>
                </div>
                {prescription.advice && (
                  <div>
                    <h4 className="font-medium">Advice:</h4>
                    <p className="text-gray-700">{prescription.advice}</p>
                  </div>
                )}
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
} 