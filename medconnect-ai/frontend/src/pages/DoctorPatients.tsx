import React, { useState, useEffect } from 'react';
import axios from 'axios';
import { Plus, Search, User } from 'lucide-react';
import DoctorMainLayout from '../components/layout/DoctorMainLayout';
import Card from '../components/ui/Card';
import Button from '../components/ui/Button';
import Badge from '../components/ui/Badge';

interface Patient {
  _id: string;
  name: string;
  email: string;
  phone?: string;
  lastVisit?: string;
  appointmentCount: number;
}

const DoctorPatients: React.FC = () => {
  const [patients, setPatients] = useState<Patient[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');

  useEffect(() => {
    const fetchPatients = async () => {
      try {
        const token = localStorage.getItem('token');
        const response = await axios.get(`${import.meta.env.VITE_API_BASE_URL}/api/appointments/doctor`, {
          headers: { Authorization: `Bearer ${token}` }
        });

        // Extract unique patients from appointments
        const appointmentsData = response.data;
        const patientsMap = new Map<string, Patient>();

        appointmentsData.forEach((appointment: any) => {
          const patientId = appointment.patientId._id;
          const patientName = appointment.patientId.name;
          const patientEmail = appointment.patientId.email;

          if (patientsMap.has(patientId)) {
            const existingPatient = patientsMap.get(patientId)!;
            existingPatient.appointmentCount += 1;

            // Update last visit if this appointment is more recent
            const appointmentDate = new Date(appointment.date);
            const lastVisitDate = existingPatient.lastVisit ? new Date(existingPatient.lastVisit) : new Date(0);
            if (appointmentDate > lastVisitDate) {
              existingPatient.lastVisit = appointment.date;
            }
          } else {
            patientsMap.set(patientId, {
              _id: patientId,
              name: patientName,
              email: patientEmail,
              lastVisit: appointment.date,
              appointmentCount: 1
            });
          }
        });

        setPatients(Array.from(patientsMap.values()));
      } catch (err) {
        console.error('Error fetching patients:', err);
        setError('Failed to load patients');
      } finally {
        setLoading(false);
      }
    };

    fetchPatients();
  }, []);

  const filteredPatients = patients.filter((patient) => {
    const matchesSearch =
      patient.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      patient.email.toLowerCase().includes(searchTerm.toLowerCase());

    return matchesSearch;
  });

  if (loading) {
    return (
      <DoctorMainLayout>
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
        </div>
      </DoctorMainLayout>
    );
  }

  if (error) {
    return (
      <DoctorMainLayout>
        <div className="text-red-500 text-center p-8">
          <p className="text-lg">{error}</p>
          <button
            onClick={() => window.location.reload()}
            className="mt-4 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
          >
            Retry
          </button>
        </div>
      </DoctorMainLayout>
    );
  }

  return (
    <DoctorMainLayout>
      <div className="space-y-6">
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">My Patients</h1>
            <p className="mt-1 text-sm text-gray-500">
              Patients who have appointments with you ({patients.length} total)
            </p>
          </div>
        </div>

        <Card>
          <div className="flex flex-col sm:flex-row justify-between gap-4 mb-6">
            {/* Search */}
            <div className="relative flex-1">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <Search size={18} className="text-gray-400" />
              </div>
              <input
                type="text"
                className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-teal-500 focus:border-teal-500 sm:text-sm"
                placeholder="Search patients by name or email..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {filteredPatients.length > 0 ? (
              filteredPatients.map((patient) => (
                <div
                  key={patient._id}
                  className="border border-gray-200 rounded-md overflow-hidden hover:border-teal-300 hover:shadow-md transition-all"
                >
                  <div className="bg-gray-50 px-4 py-3 border-b border-gray-200 flex justify-between items-center">
                    <div className="flex items-center">
                      <div className="h-10 w-10 rounded-full bg-teal-100 flex items-center justify-center">
                        <User className="h-5 w-5 text-teal-600" />
                      </div>
                      <div className="ml-3">
                        <h3 className="text-sm font-medium text-gray-900">{patient.name}</h3>
                        <p className="text-xs text-gray-500">{patient.email}</p>
                      </div>
                    </div>
                    <Badge variant="info">
                      {patient.appointmentCount} visits
                    </Badge>
                  </div>
                  <div className="px-4 py-3">
                    <div className="flex justify-between text-sm mb-2">
                      <span className="text-gray-500">Total Appointments:</span>
                      <span className="font-medium text-gray-900">{patient.appointmentCount}</span>
                    </div>
                    <div className="flex justify-between text-sm mb-2">
                      <span className="text-gray-500">Last Visit:</span>
                      <span className="font-medium text-gray-900">
                        {patient.lastVisit ? new Date(patient.lastVisit).toLocaleDateString() : 'N/A'}
                      </span>
                    </div>

                    <div className="mt-4 pt-3 border-t border-gray-100 flex justify-between">
                      <Button variant="outline" size="sm">
                        View History
                      </Button>
                      <Button variant="primary" size="sm">
                        Add Prescription
                      </Button>
                    </div>
                  </div>
                </div>
              ))
            ) : (
              <div className="col-span-full text-center py-4 text-gray-500">
                {patients.length === 0 ? 'No patients found. Patients will appear here after they book appointments with you.' : 'No patients found matching your search criteria'}
              </div>
            )}
          </div>
        </Card>
      </div>
    </DoctorMainLayout>
  );
};

export default DoctorPatients;
