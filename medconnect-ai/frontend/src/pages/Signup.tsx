import React, { useState, useEffect, useRef } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { gsap } from 'gsap';
import { User, Mail, Lock, Eye, EyeOff, UserCog } from 'lucide-react';
import LandingFormInput from '../components/ui/LandingFormInput';
import LandingAuthLayout from '../components/ui/LandingAuthLayout';
import LandingFormSelect from '../components/ui/LandingFormSelect';
import LandingAuthHeader from '../components/ui/LandingAuthHeader';
import LandingFormButton from '../components/ui/LandingFormButton';
import LandingSocialLogin from '../components/ui/LandingSocialLogin';
import LandingPasswordStrength from '../components/ui/LandingPasswordStrength';
import { authService } from '../services/api';
import type { SignupFormData } from '../types/auth';

export default function Signup() {
  const navigate = useNavigate();
  const [fullName, setFullName] = useState('');
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [role, setRole] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [errors, setErrors] = useState<{
    fullName?: string;
    email?: string;
    password?: string;
    role?: string;
  }>({});
  const [error, setError] = useState<string>('');

  const formRef = useRef<HTMLFormElement>(null);

  const roleOptions = [
    { value: 'patient', label: 'Patient' },
    { value: 'doctor', label: 'Doctor' },
  ];

  useEffect(() => {
    // Animate form elements
    const tl = gsap.timeline();

    tl.fromTo(
      '.form-element',
      {
        opacity: 0,
        y: 20,
      },
      {
        opacity: 1,
        y: 0,
        stagger: 0.1,
        ease: 'power2.out',
      },
    );
  }, []);

  const validate = () => {
    const newErrors: {
      fullName?: string;
      email?: string;
      password?: string;
      role?: string;
    } = {};
    let isValid = true;

    if (!fullName.trim()) {
      newErrors.fullName = 'Full name is required';
      isValid = false;
    }

    if (!email.trim()) {
      newErrors.email = 'Email is required';
      isValid = false;
    } else if (!/\S+@\S+\.\S+/.test(email)) {
      newErrors.email = 'Email address is invalid';
      isValid = false;
    }

    if (!password) {
      newErrors.password = 'Password is required';
      isValid = false;
    } else if (password.length < 8) {
      newErrors.password = 'Password must be at least 8 characters';
      isValid = false;
    }

    if (!role) {
      newErrors.role = 'Please select a role';
      isValid = false;
    }

    setErrors(newErrors);
    return isValid;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validate()) return;

    setIsLoading(true);
    setError('');

    try {
      const formData: SignupFormData = {
        name: fullName,
        email,
        password,
        role: role as 'patient' | 'doctor'
      };
      await authService.signup(formData);
      alert('Registration successful! Please login.');
      navigate('/login');
    } catch (err: any) {
      setError(err.response?.data?.error || 'Registration failed. Please try again.');
    } finally {
      setIsLoading(false);
    }

    // Animate button on submit
    gsap.to('.submit-btn', {
      scale: 0.95,
      duration: 0.1,
      yoyo: true,
      repeat: 1,
    });
  };

  const togglePasswordVisibility = () => {
    setShowPassword(!showPassword);
  };

  return (
    <LandingAuthLayout>
      <div className="page-transition w-full">
        <form
          ref={formRef}
          onSubmit={handleSubmit}
          className="w-full"
        >
          <LandingAuthHeader
            title="Create Your Account"
            subtitle="Join MedConnect AI to access personalized healthcare services"
          />

          {error && (
            <div className="rounded-md bg-red-50 p-4 mb-4">
              <div className="text-sm text-red-700">{error}</div>
            </div>
          )}

          <div className="space-y-5">
            <div className="form-element">
              <LandingFormInput
                label="Full Name"
                type="text"
                id="fullName"
                value={fullName}
                onChange={(e) => {
                  setFullName(e.target.value);
                  if (errors.fullName) setErrors({...errors, fullName: undefined});
                }}
                placeholder="Enter your full name"
                icon={<User size={18} />}
                required
                error={errors.fullName}
              />
            </div>

            <div className="form-element">
              <LandingFormInput
                label="Email"
                type="email"
                id="email"
                value={email}
                onChange={(e) => {
                  setEmail(e.target.value);
                  if (errors.email) setErrors({...errors, email: undefined});
                }}
                placeholder="Enter your email address"
                icon={<Mail size={18} />}
                required
                error={errors.email}
              />
            </div>

            <div className="form-element">
              <LandingFormInput
                label="Password"
                type={showPassword ? 'text' : 'password'}
                id="password"
                value={password}
                onChange={(e) => {
                  setPassword(e.target.value);
                  if (errors.password) setErrors({...errors, password: undefined});
                }}
                placeholder="Create a password"
                icon={<Lock size={18} />}
                required
                error={errors.password}
                rightIcon={
                  showPassword ?
                    <Eye
                      size={18}
                      className="cursor-pointer text-slate-400 hover:text-slate-600"
                      onClick={togglePasswordVisibility}
                    /> :
                    <EyeOff
                      size={18}
                      className="cursor-pointer text-slate-400 hover:text-slate-600"
                      onClick={togglePasswordVisibility}
                    />
                }
              />
              <LandingPasswordStrength password={password} />
            </div>

            <div className="form-element">
              <LandingFormSelect
                label="Role"
                id="role"
                value={role}
                onChange={(e) => {
                  setRole(e.target.value);
                  if (errors.role) setErrors({...errors, role: undefined});
                }}
                options={roleOptions}
                placeholder="Select your role"
                icon={<UserCog size={18} />}
                required
                error={errors.role}
              />
            </div>

            <div className="form-element">
              <LandingFormButton
                type="submit"
                variant="primary"
                isLoading={isLoading}
              >
                Sign Up
              </LandingFormButton>
            </div>

            <LandingSocialLogin />

            <p className="form-element text-center text-sm text-slate-600 mt-6">
              Already have an account? {' '}
              <Link to="/login" className="link">
                Login
              </Link>
            </p>
          </div>
        </form>
      </div>
    </LandingAuthLayout>
  );
}