import React, { useState } from 'react';
import axios from 'axios';

export default function SymptomChecker() {
  const [symptoms, setSymptoms] = useState<string[]>([]);
  const [currentSymptom, setCurrentSymptom] = useState('');
  const [analysis, setAnalysis] = useState<string>('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');

  const handleAddSymptom = () => {
    if (currentSymptom.trim()) {
      setSymptoms([...symptoms, currentSymptom.trim()]);
      setCurrentSymptom('');
      setError(''); // Clear any existing errors
    }
  };

  const handleRemoveSymptom = (index: number) => {
    setSymptoms(symptoms.filter((_, i) => i !== index));
    setError(''); // Clear any existing errors
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (symptoms.length === 0) {
      setError('Please add at least one symptom');
      return;
    }

    setLoading(true);
    setError('');
    setAnalysis(''); // Clear previous analysis

    try {
      const token = localStorage.getItem('token');
      if (!token) {
        throw new Error('Authentication token not found');
      }

      const response = await axios.post(
        `${import.meta.env.VITE_API_BASE_URL}/api/ai/symptom`,
        { symptoms },
        {
          headers: {
            Authorization: `Bearer ${token}`,
            'Content-Type': 'application/json'
          }
        }
      );

      if (response.data.error) {
        throw new Error(response.data.error);
      }

      setAnalysis(response.data.analysis);
    } catch (err: any) {
      console.error('Symptom analysis error:', err);

      // Handle different types of errors
      if (err.response?.status === 401) {
        setError('Please log in again to use this feature');
      } else if (err.response?.status === 500) {
        setError(err.response.data.message || 'Server error. Please try again later.');
      } else if (!navigator.onLine) {
        setError('Please check your internet connection');
      } else {
        setError(err.message || 'Failed to analyze symptoms. Please try again.');
      }
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="py-4">
      <h1 className="text-2xl font-bold text-gray-800 mb-6">AI Symptom Checker</h1>
      <div className="bg-white shadow-sm border border-gray-200 rounded-lg p-6">

        <div className="mb-8">
          <div className="flex gap-2 mb-2">
            <input
              type="text"
              value={currentSymptom}
              onChange={(e) => {
                setCurrentSymptom(e.target.value);
                if (error) setError(''); // Clear error when user starts typing
              }}
              onKeyPress={(e) => e.key === 'Enter' && handleAddSymptom()}
              placeholder="Enter a symptom (e.g., headache, fever)"
              className="flex-1 p-2 border rounded"
              disabled={loading}
            />
            <button
              type="button"
              onClick={handleAddSymptom}
              className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 disabled:bg-gray-400"
              disabled={loading || !currentSymptom.trim()}
            >
              Add
            </button>
          </div>

          {symptoms.length > 0 && (
            <div className="flex flex-wrap gap-2 mb-4">
              {symptoms.map((symptom, index) => (
                <div
                  key={index}
                  className="flex items-center bg-gray-100 px-3 py-1 rounded"
                >
                  <span>{symptom}</span>
                  <button
                    onClick={() => handleRemoveSymptom(index)}
                    className="ml-2 text-red-500 hover:text-red-700"
                    disabled={loading}
                  >
                    ×
                  </button>
                </div>
              ))}
            </div>
          )}

          <button
            onClick={handleSubmit}
            disabled={loading || symptoms.length === 0}
            className={`w-full py-2 px-4 rounded text-white ${
              loading || symptoms.length === 0
                ? 'bg-gray-400 cursor-not-allowed'
                : 'bg-green-500 hover:bg-green-600'
            }`}
          >
            {loading ? (
              <span className="flex items-center justify-center">
                <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                Analyzing...
              </span>
            ) : 'Analyze Symptoms'}
          </button>
        </div>

        {error && (
          <div className="mb-4 p-3 bg-red-50 border border-red-200 text-red-700 rounded">
            <div className="font-medium">Error:</div>
            <div>{error}</div>
          </div>
        )}

        {analysis && (
          <div className="mt-6">
            <h2 className="text-xl font-semibold mb-4">Analysis Results</h2>
            <div className="bg-blue-50 border border-blue-200 rounded p-4">
              <pre className="whitespace-pre-wrap font-sans text-gray-800">
                {analysis}
              </pre>
            </div>
            <div className="mt-4 text-sm text-gray-500">
              <p className="font-medium mb-1">Important Notice:</p>
              <ul className="list-disc list-inside space-y-1">
                <li>This is an AI-powered analysis and should not replace professional medical advice.</li>
                <li>If you're experiencing severe symptoms, please seek immediate medical attention.</li>
                <li>For accurate diagnosis, consult with a healthcare provider.</li>
              </ul>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}