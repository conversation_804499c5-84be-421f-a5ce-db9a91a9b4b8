import { useEffect, useState } from 'react';
import axios from 'axios';
import { Link } from 'react-router-dom';
import { Calendar, FilePen, UserCheck, Activity } from 'lucide-react';
import DoctorMainLayout from '../components/layout/DoctorMainLayout';
import StatCard from '../components/ui/StatCard';
import Card from '../components/ui/Card';
import Button from '../components/ui/Button';
import Badge from '../components/ui/Badge';

interface DoctorData {
  profile: {
    name: string;
    email: string;
    specialization: string;
    experience: number;
    qualifications: string[];
  };
  statistics: {
    totalPatients: number;
    appointmentsToday: number;
    appointmentsThisWeek: number;
    completedAppointments: number;
  };
  todayAppointments: Array<{
    _id: string;
    patientId: {
      name: string;
      _id: string;
    };
    date: string;
    time: string;
    status: string;
    type?: string;
    notes?: string;
  }>;
  upcomingAppointments: any[];
}

const DoctorDashboard = () => {
  const [data, setData] = useState<DoctorData | null>(null);
  const [error, setError] = useState('');
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchDashboard = async () => {
      try {
        const token = localStorage.getItem('token');
        const response = await axios.get('http://localhost:5000/api/doctor/dashboard', {
          headers: { Authorization: `Bearer ${token}` }
        });
        setData(response.data);
      } catch (err) {
        console.error('Dashboard error:', err);
        setError('Failed to load dashboard');
      } finally {
        setLoading(false);
      }
    };

    fetchDashboard();
  }, []);

  if (loading) {
    return (
      <DoctorMainLayout doctorName="Loading...">
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
        </div>
      </DoctorMainLayout>
    );
  }

  if (error) {
    return (
      <DoctorMainLayout doctorName="Doctor">
        <div className="text-red-500 text-center p-8">
          <p className="text-lg">{error}</p>
          <Button
            onClick={() => window.location.reload()}
            className="mt-4"
            variant="primary"
          >
            Retry
          </Button>
        </div>
      </DoctorMainLayout>
    );
  }

  if (!data) {
    return (
      <DoctorMainLayout doctorName="Doctor">
        <div className="text-center p-8">
          <p className="text-gray-500">No data available</p>
        </div>
      </DoctorMainLayout>
    );
  }

  const doctorFirstName = data.profile.name.split(' ')[1] || data.profile.name.split(' ')[0];

  return (
    <DoctorMainLayout doctorName={doctorFirstName}>
      <div className="space-y-6">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Dashboard</h1>
          <p className="mt-1 text-sm text-gray-500">
            Welcome back, Dr. {doctorFirstName}. Here's what's happening today.
          </p>
        </div>

        {/* Stats Overview */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <StatCard
            title="Appointments Today"
            value={data.statistics.appointmentsToday}
            icon={<Calendar size={24} />}
          />
          <StatCard
            title="Total Patients"
            value={data.statistics.totalPatients}
            icon={<UserCheck size={24} />}
          />
          <StatCard
            title="This Week"
            value={data.statistics.appointmentsThisWeek}
            icon={<FilePen size={24} />}
          />
        </div>

        {/* Doctor Profile Card */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          <Card title="Doctor Profile" icon={<Activity size={20} />} className="lg:col-span-1">
            <div className="space-y-3">
              <div>
                <p className="text-sm font-medium text-gray-500">Name</p>
                <p className="text-gray-900">{data.profile.name}</p>
              </div>
              <div>
                <p className="text-sm font-medium text-gray-500">Specialization</p>
                <p className="text-gray-900">{data.profile.specialization}</p>
              </div>
              <div>
                <p className="text-sm font-medium text-gray-500">Experience</p>
                <p className="text-gray-900">{data.profile.experience} years</p>
              </div>
              <div>
                <p className="text-sm font-medium text-gray-500">Email</p>
                <p className="text-gray-900 text-sm">{data.profile.email}</p>
              </div>
            </div>
          </Card>

          {/* Quick Actions */}
          <Card title="Quick Actions" className="lg:col-span-2">
            <div className="flex flex-wrap gap-4">
              <Link to="/doctor/appointments">
                <Button
                  variant="primary"
                  icon={<Calendar size={16} />}
                >
                  Go to Today's Schedule
                </Button>
              </Link>
              <Link to="/doctor/generate-prescription">
                <Button
                  variant="secondary"
                  icon={<FilePen size={16} />}
                >
                  Write New Prescription
                </Button>
              </Link>
            </div>
          </Card>
        </div>

        {/* Today's Appointments */}
        <Card title="Today's Appointments" className="overflow-hidden">
          <div className="flow-root">
            <div className="-mx-4 -my-2 overflow-x-auto">
              <div className="inline-block min-w-full py-2 align-middle">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      <th
                        scope="col"
                        className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                      >
                        PATIENT
                      </th>
                      <th
                        scope="col"
                        className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                      >
                        TIME
                      </th>
                      <th
                        scope="col"
                        className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                      >
                        SYMPTOMS
                      </th>
                      <th
                        scope="col"
                        className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                      >
                        STATUS
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {data.todayAppointments.length > 0 ? (
                      data.todayAppointments.map((appointment) => (
                        <tr key={appointment._id} className="hover:bg-gray-50">
                          <td className="px-4 py-4 whitespace-nowrap">
                            <div className="text-sm font-medium text-gray-900">
                              {appointment.patientId.name}
                            </div>
                          </td>
                          <td className="px-4 py-4 whitespace-nowrap">
                            <div className="text-sm text-gray-500">{appointment.time}</div>
                          </td>
                          <td className="px-4 py-4">
                            <div className="text-sm text-gray-500">
                              {appointment.type ? appointment.type.charAt(0).toUpperCase() + appointment.type.slice(1) : 'Consultation'}
                            </div>
                            {appointment.notes && (
                              <div className="text-xs text-gray-400 mt-1 italic">
                                {appointment.notes}
                              </div>
                            )}
                          </td>
                          <td className="px-4 py-4 whitespace-nowrap">
                            <Badge
                              variant={
                                appointment.status === 'pending' ? 'warning' :
                                appointment.status === 'approved' ? 'success' :
                                appointment.status === 'cancelled' ? 'danger' : 'info'
                              }
                            >
                              {appointment.status}
                            </Badge>
                          </td>
                        </tr>
                      ))
                    ) : (
                      <tr>
                        <td colSpan={4} className="px-4 py-8 text-center text-sm text-gray-500">
                          <div className="flex flex-col items-center">
                            <Calendar size={48} className="text-gray-300 mb-2" />
                            <p>No appointments scheduled for today</p>
                          </div>
                        </td>
                      </tr>
                    )}
                  </tbody>
                </table>
                {data.todayAppointments.length > 0 && (
                  <div className="py-3 px-4 border-t border-gray-200">
                    <Link to="/doctor/appointments">
                      <Button variant="outline" size="sm">
                        View All Appointments
                      </Button>
                    </Link>
                  </div>
                )}
              </div>
            </div>
          </div>
        </Card>
      </div>
    </DoctorMainLayout>
  );
};

export default DoctorDashboard;