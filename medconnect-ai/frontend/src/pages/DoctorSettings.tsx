import React, { useState } from 'react';
import DoctorMainLayout from '../components/layout/DoctorMainLayout';
import Card from '../components/ui/Card';
import Button from '../components/ui/Button';

const DoctorSettings: React.FC = () => {
  const [doctor, setDoctor] = useState({
    name: 'Dr. <PERSON>',
    email: '<EMAIL>',
    specialty: 'Cardiologist',
    phone: '+****************',
    address: '123 Medical Center Dr, San Francisco, CA 94143',
    bio: 'Board-certified cardiologist with over 10 years of experience specializing in cardiac imaging and preventive cardiology.',
  });

  const [notifications, setNotifications] = useState({
    email: true,
    sms: false,
    appointmentReminders: true,
    patientUpdates: true,
    systemUpdates: true,
  });

  const handleDoctorChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setDoctor(prev => ({ ...prev, [name]: value }));
  };

  const handleNotificationChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, checked } = e.target;
    setNotifications(prev => ({ ...prev, [name]: checked }));
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    // In a real app, this would save the settings
    console.log({ doctor, notifications });
  };

  return (
    <DoctorMainLayout>
      <div className="space-y-6">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Settings</h1>
          <p className="mt-1 text-sm text-gray-500">
            Manage your account settings and preferences
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          {/* Profile Settings */}
          <div className="md:col-span-2">
            <Card title="Profile Settings">
              <form onSubmit={handleSubmit}>
                <div className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-1">
                        Full Name
                      </label>
                      <input
                        type="text"
                        id="name"
                        name="name"
                        value={doctor.name}
                        onChange={handleDoctorChange}
                        className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-teal-500 focus:border-teal-500 sm:text-sm"
                      />
                    </div>
                    <div>
                      <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-1">
                        Email
                      </label>
                      <input
                        type="email"
                        id="email"
                        name="email"
                        value={doctor.email}
                        onChange={handleDoctorChange}
                        className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-teal-500 focus:border-teal-500 sm:text-sm"
                      />
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label htmlFor="specialty" className="block text-sm font-medium text-gray-700 mb-1">
                        Specialty
                      </label>
                      <input
                        type="text"
                        id="specialty"
                        name="specialty"
                        value={doctor.specialty}
                        onChange={handleDoctorChange}
                        className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-teal-500 focus:border-teal-500 sm:text-sm"
                      />
                    </div>
                    <div>
                      <label htmlFor="phone" className="block text-sm font-medium text-gray-700 mb-1">
                        Phone
                      </label>
                      <input
                        type="text"
                        id="phone"
                        name="phone"
                        value={doctor.phone}
                        onChange={handleDoctorChange}
                        className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-teal-500 focus:border-teal-500 sm:text-sm"
                      />
                    </div>
                  </div>

                  <div>
                    <label htmlFor="address" className="block text-sm font-medium text-gray-700 mb-1">
                      Address
                    </label>
                    <input
                      type="text"
                      id="address"
                      name="address"
                      value={doctor.address}
                      onChange={handleDoctorChange}
                      className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-teal-500 focus:border-teal-500 sm:text-sm"
                    />
                  </div>

                  <div>
                    <label htmlFor="bio" className="block text-sm font-medium text-gray-700 mb-1">
                      Professional Bio
                    </label>
                    <textarea
                      id="bio"
                      name="bio"
                      rows={4}
                      value={doctor.bio}
                      onChange={handleDoctorChange}
                      className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-teal-500 focus:border-teal-500 sm:text-sm"
                    />
                  </div>

                  <div>
                    <button
                      type="button"
                      className="text-sm text-teal-600 hover:text-teal-700"
                    >
                      Change Password
                    </button>
                  </div>

                  <div className="flex justify-end">
                    <Button type="submit">Save Changes</Button>
                  </div>
                </div>
              </form>
            </Card>
          </div>

          {/* Notification Settings */}
          <div>
            <Card title="Notification Settings">
              <form onSubmit={handleSubmit}>
                <div className="space-y-4">
                  <fieldset>
                    <legend className="text-sm font-medium text-gray-700 mb-2">
                      Notification Methods
                    </legend>
                    <div className="space-y-2">
                      <div className="flex items-center">
                        <input
                          id="email-notifications"
                          name="email"
                          type="checkbox"
                          checked={notifications.email}
                          onChange={handleNotificationChange}
                          className="h-4 w-4 text-teal-600 focus:ring-teal-500 border-gray-300 rounded"
                        />
                        <label
                          htmlFor="email-notifications"
                          className="ml-2 block text-sm text-gray-700"
                        >
                          Email Notifications
                        </label>
                      </div>
                      <div className="flex items-center">
                        <input
                          id="sms-notifications"
                          name="sms"
                          type="checkbox"
                          checked={notifications.sms}
                          onChange={handleNotificationChange}
                          className="h-4 w-4 text-teal-600 focus:ring-teal-500 border-gray-300 rounded"
                        />
                        <label
                          htmlFor="sms-notifications"
                          className="ml-2 block text-sm text-gray-700"
                        >
                          SMS Notifications
                        </label>
                      </div>
                    </div>
                  </fieldset>

                  <fieldset>
                    <legend className="text-sm font-medium text-gray-700 mb-2">
                      Notification Types
                    </legend>
                    <div className="space-y-2">
                      <div className="flex items-center">
                        <input
                          id="appointment-reminders"
                          name="appointmentReminders"
                          type="checkbox"
                          checked={notifications.appointmentReminders}
                          onChange={handleNotificationChange}
                          className="h-4 w-4 text-teal-600 focus:ring-teal-500 border-gray-300 rounded"
                        />
                        <label
                          htmlFor="appointment-reminders"
                          className="ml-2 block text-sm text-gray-700"
                        >
                          Appointment Reminders
                        </label>
                      </div>
                      <div className="flex items-center">
                        <input
                          id="patient-updates"
                          name="patientUpdates"
                          type="checkbox"
                          checked={notifications.patientUpdates}
                          onChange={handleNotificationChange}
                          className="h-4 w-4 text-teal-600 focus:ring-teal-500 border-gray-300 rounded"
                        />
                        <label
                          htmlFor="patient-updates"
                          className="ml-2 block text-sm text-gray-700"
                        >
                          Patient Updates
                        </label>
                      </div>
                      <div className="flex items-center">
                        <input
                          id="system-updates"
                          name="systemUpdates"
                          type="checkbox"
                          checked={notifications.systemUpdates}
                          onChange={handleNotificationChange}
                          className="h-4 w-4 text-teal-600 focus:ring-teal-500 border-gray-300 rounded"
                        />
                        <label
                          htmlFor="system-updates"
                          className="ml-2 block text-sm text-gray-700"
                        >
                          System Updates
                        </label>
                      </div>
                    </div>
                  </fieldset>

                  <div className="pt-2">
                    <Button type="submit" variant="outline" size="sm">
                      Save Preferences
                    </Button>
                  </div>
                </div>
              </form>
            </Card>

            <Card title="Account Settings" className="mt-6">
              <div className="space-y-4">
                <button className="text-sm text-blue-600 hover:text-blue-700 block">
                  Export My Data
                </button>
                <button className="text-sm text-red-600 hover:text-red-700 block">
                  Delete Account
                </button>
              </div>
            </Card>
          </div>
        </div>
      </div>
    </DoctorMainLayout>
  );
};

export default DoctorSettings;
