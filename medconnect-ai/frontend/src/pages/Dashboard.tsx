import React, { useEffect, useState } from 'react';
import axios from 'axios';
import QuickActions from '../components/dashboard/QuickActions';
import AppointmentsList from '../components/dashboard/AppointmentsList';
import PrescriptionsList from '../components/dashboard/PrescriptionsList';
import AIInsightsPanel from '../components/dashboard/AIInsightsPanel';
import HealthMonitoring from '../components/dashboard/HealthMonitoring';
import { Appointment } from '../types/appointment';
import { Prescription } from '../types/prescription';

interface PatientDashboardData {
  profile: {
    name: string;
    email: string;
    phone?: string;
    dateOfBirth?: string;
    medicalHistory: string[];
  };
  appointments: any[];
  upcomingAppointments: any[];
  pastAppointments: any[];
}

interface SymptomCheck {
  date: string;
  symptoms: string[];
  likelyDiagnosis: string;
  severity: 'low' | 'medium' | 'high';
  recommendation: string;
}

const Dashboard: React.FC = () => {
  const [dashboardData, setDashboardData] = useState<PatientDashboardData | null>(null);
  const [prescriptions, setPrescriptions] = useState<Prescription[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');

  useEffect(() => {
    const fetchDashboardData = async () => {
      try {
        const token = localStorage.getItem('token');
        const apiBaseUrl = import.meta.env.VITE_API_BASE_URL || 'http://localhost:5000';

        if (!token) {
          throw new Error('No authentication token found');
        }

        // Fetch dashboard data
        const dashboardResponse = await axios.get(`${apiBaseUrl}/api/patient/dashboard`, {
          headers: { Authorization: `Bearer ${token}` }
        });

        // Fetch prescriptions
        const prescriptionsResponse = await axios.get(`${apiBaseUrl}/api/prescriptions/patient`, {
          headers: { Authorization: `Bearer ${token}` }
        });

        setDashboardData(dashboardResponse.data);
        setPrescriptions(prescriptionsResponse.data);
      } catch (err: any) {
        console.error('Dashboard error:', err);
        console.error('Error details:', err.response?.data);

        let errorMessage = 'Failed to load dashboard data';
        if (err.message === 'No authentication token found') {
          errorMessage = 'Please log in again';
          // Redirect to login
          window.location.href = '/login';
          return;
        } else if (err.response?.status === 401) {
          errorMessage = 'Session expired. Please log in again';
          // Clear token and redirect
          localStorage.removeItem('token');
          window.location.href = '/login';
          return;
        } else if (err.response?.status === 403) {
          errorMessage = 'Access denied. Please check your account permissions';
        } else if (err.code === 'ERR_NETWORK' || err.message.includes('Network Error')) {
          errorMessage = 'Cannot connect to server. Please check if the backend is running';
        }

        setError(errorMessage);
      } finally {
        setLoading(false);
      }
    };

    fetchDashboardData();
  }, []);

  // Transform appointments data to match component interface
  const transformAppointments = (appointments: any[]): Appointment[] => {
    return appointments.map(apt => ({
      id: apt._id,
      doctorName: apt.doctorId?.name || 'Unknown Doctor',
      specialty: apt.doctorId?.specialization || 'General Practice',
      date: new Date(apt.date).toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
      }),
      time: apt.time,
      location: 'MedConnect Clinic', // Default location since not in backend
      status: apt.status as 'confirmed' | 'pending' | 'rescheduled' | 'cancelled',
      notes: apt.notes || ''
    }));
  };

  // Transform prescriptions data to match component interface
  const transformPrescriptions = (prescriptions: any[]): Prescription[] => {
    return prescriptions.map(presc => ({
      id: presc._id,
      medication: presc.medicines?.[0]?.name || 'Unknown Medication',
      dosage: presc.medicines?.[0]?.dosage || 'As prescribed',
      frequency: presc.medicines?.[0]?.timing || 'As directed',
      instructions: presc.advice || 'Follow doctor\'s instructions',
      prescribedBy: presc.doctorId?.name || 'Unknown Doctor',
      prescribedDate: new Date(presc.createdAt).toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
      }),
      expiryDate: 'Not specified',
      refillsLeft: 0, // Not tracked in current backend
      pharmacy: {
        name: 'MedConnect Pharmacy',
        address: '123 Health St, Medical Center',
        phone: '(*************'
      }
    }));
  };

  // Mock last symptom check for now (can be enhanced later)
  const mockLastSymptomCheck: SymptomCheck = {
    date: 'No recent checks',
    symptoms: [],
    likelyDiagnosis: 'No recent analysis',
    severity: 'low',
    recommendation: 'Use the AI Symptom Checker to analyze your symptoms.'
  };

  if (loading) {
    return (
      <div className="py-4">
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="py-4">
        <div className="text-red-500 text-center p-8">
          <p className="text-lg">{error}</p>
          <button
            onClick={() => window.location.reload()}
            className="mt-4 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
          >
            Retry
          </button>
        </div>
      </div>
    );
  }

  if (!dashboardData) {
    return (
      <div className="py-4">
        <div className="text-center p-8">
          <p className="text-gray-500">No data available</p>
        </div>
      </div>
    );
  }

  const upcomingAppointments = transformAppointments(dashboardData.upcomingAppointments || []);
  const transformedPrescriptions = transformPrescriptions(prescriptions);

  return (
    <div className="py-4">
      <h1 className="text-2xl font-bold text-gray-800 mb-6">
        Welcome back, {dashboardData.profile.name.split(' ')[0]}!
      </h1>

      <QuickActions />
      <HealthMonitoring />

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <div>
          <AppointmentsList appointments={upcomingAppointments} />
        </div>
        <div>
          <PrescriptionsList prescriptions={transformedPrescriptions} />
        </div>
      </div>

      <AIInsightsPanel lastSymptomCheck={mockLastSymptomCheck} />
    </div>
  );
};

export default Dashboard;
