import React, { useState, useEffect, useRef } from 'react';
import axios from 'axios';
import { Plus, Trash2 } from 'lucide-react';
import DoctorMainLayout from '../components/layout/DoctorMainLayout';
import Card from '../components/ui/Card';
import Button from '../components/ui/Button';

interface Patient {
  _id: string;
  name: string;
  email: string;
}

const DoctorGeneratePrescription: React.FC = () => {
  const [patients, setPatients] = useState<Patient[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedPatient, setSelectedPatient] = useState('');
  const [selectedPatientName, setSelectedPatientName] = useState('');
  const [showDropdown, setShowDropdown] = useState(false);
  const [diagnosis, setDiagnosis] = useState('');
  const [advice, setAdvice] = useState('');
  const [followUp, setFollowUp] = useState('');
  const [loading, setLoading] = useState(false);
  const [searchLoading, setSearchLoading] = useState(false);
  const [error, setError] = useState('');
  const [medicines, setMedicines] = useState([
    { name: '', dosage: '', duration: '', timing: '' },
  ]);

  const dropdownRef = useRef<HTMLDivElement>(null);

  // Fetch patients when search query changes
  useEffect(() => {
    const fetchPatients = async () => {
      if (searchQuery.length < 2) {
        setPatients([]);
        setShowDropdown(false);
        setSearchLoading(false);
        return;
      }

      setSearchLoading(true);
      try {
        const token = localStorage.getItem('token');
        console.log('Searching for patients with query:', searchQuery);
        const response = await axios.get(`${import.meta.env.VITE_API_BASE_URL}/api/users/search?query=${searchQuery}&role=patient`, {
          headers: { Authorization: `Bearer ${token}` }
        });
        console.log('Patient search response:', response.data);
        setPatients(response.data);
        setShowDropdown(response.data.length > 0);
      } catch (err) {
        console.error('Error fetching patients:', err);
        setPatients([]);
        setShowDropdown(false);
      } finally {
        setSearchLoading(false);
      }
    };

    const debounceTimer = setTimeout(fetchPatients, 300);
    return () => clearTimeout(debounceTimer);
  }, [searchQuery]);

  // Handle click outside to close dropdown
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setShowDropdown(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  const handleAddMedicine = () => {
    setMedicines([
      ...medicines,
      { name: '', dosage: '', duration: '', timing: '' },
    ]);
  };

  const handleRemoveMedicine = (index: number) => {
    const updatedMedicines = [...medicines];
    updatedMedicines.splice(index, 1);
    setMedicines(updatedMedicines);
  };

  const handleMedicineChange = (
    index: number,
    field: 'name' | 'dosage' | 'duration' | 'timing',
    value: string
  ) => {
    const updatedMedicines = [...medicines];
    updatedMedicines[index][field] = value;
    setMedicines(updatedMedicines);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!selectedPatient || !diagnosis || medicines.some(med => !med.name)) {
      setError('Please fill in all required fields');
      return;
    }

    setLoading(true);
    setError('');

    try {
      const token = localStorage.getItem('token');
      const prescriptionData = {
        patientId: selectedPatient,
        diagnosis,
        medicines: medicines.filter(med => med.name.trim() !== ''),
        advice: advice || 'Follow the prescribed medication schedule.'
      };

      await axios.post(`${import.meta.env.VITE_API_BASE_URL}/api/prescriptions/add`, prescriptionData, {
        headers: { Authorization: `Bearer ${token}` }
      });

      // Reset form
      setSelectedPatient('');
      setSelectedPatientName('');
      setSearchQuery('');
      setShowDropdown(false);
      setDiagnosis('');
      setAdvice('');
      setFollowUp('');
      setMedicines([{ name: '', dosage: '', duration: '', timing: '' }]);
      setPatients([]);

      alert('Prescription saved successfully!');
    } catch (err) {
      console.error('Error saving prescription:', err);
      setError('Failed to save prescription. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  return (
    <DoctorMainLayout>
      <div className="space-y-6">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Generate Prescription</h1>
          <p className="mt-1 text-sm text-gray-500">
            Create a new prescription for your patient
          </p>
        </div>

        <Card>
          <form onSubmit={handleSubmit}>
            <div className="space-y-6">
              {/* Patient Selection */}
              <div>
                <label
                  htmlFor="patient"
                  className="block text-sm font-medium text-gray-700 mb-1"
                >
                  Search Patient *
                </label>
                <div className="relative" ref={dropdownRef}>
                  <input
                    type="text"
                    id="patient"
                    placeholder="Type patient name to search..."
                    className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-teal-500 focus:border-teal-500 sm:text-sm"
                    value={searchQuery}
                    onChange={(e) => {
                      const value = e.target.value;
                      setSearchQuery(value);
                      setSelectedPatient('');
                      setSelectedPatientName('');
                      setError('');

                      // Show dropdown if we have results and user is typing
                      if (value.length >= 2 && patients.length > 0) {
                        setShowDropdown(true);
                      }
                    }}
                    onFocus={() => {
                      if (patients.length > 0 && searchQuery.length >= 2) {
                        setShowDropdown(true);
                      }
                    }}
                    onKeyDown={(e) => {
                      if (e.key === 'Escape') {
                        setShowDropdown(false);
                      }
                    }}
                  />
                  {showDropdown && patients.length > 0 && (
                    <div className="absolute z-50 w-full bg-white border border-gray-300 rounded-md shadow-lg mt-1 max-h-60 overflow-auto">
                      {patients.map((patient) => (
                        <button
                          key={patient._id}
                          type="button"
                          className="w-full text-left px-3 py-2 hover:bg-gray-50 focus:bg-gray-50 focus:outline-none border-b border-gray-100 last:border-b-0"
                          onClick={() => {
                            setSelectedPatient(patient._id);
                            setSelectedPatientName(patient.name);
                            setSearchQuery(patient.name);
                            setShowDropdown(false);
                            setPatients([]);
                            setError('');
                          }}
                        >
                          <div className="font-medium text-gray-900">{patient.name}</div>
                          <div className="text-sm text-gray-500">{patient.email}</div>
                        </button>
                      ))}
                    </div>
                  )}
                  {searchLoading && searchQuery.length >= 2 && (
                    <div className="absolute z-50 w-full bg-white border border-gray-300 rounded-md shadow-lg mt-1 p-3">
                      <div className="text-sm text-gray-500 flex items-center">
                        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-teal-600 mr-2"></div>
                        Searching patients...
                      </div>
                    </div>
                  )}
                  {searchQuery.length >= 2 && patients.length === 0 && !searchLoading && (
                    <div className="absolute z-50 w-full bg-white border border-gray-300 rounded-md shadow-lg mt-1 p-3">
                      <div className="text-sm text-gray-500">No patients found matching "{searchQuery}"</div>
                    </div>
                  )}
                  {searchQuery.length === 1 && (
                    <div className="absolute z-50 w-full bg-white border border-gray-300 rounded-md shadow-lg mt-1 p-3">
                      <div className="text-sm text-gray-400">Type at least 2 characters to search...</div>
                    </div>
                  )}
                </div>
                {selectedPatientName && (
                  <div className="mt-2 p-2 bg-green-50 border border-green-200 rounded-md">
                    <div className="text-sm text-green-800">
                      Selected: <span className="font-medium">{selectedPatientName}</span>
                    </div>
                  </div>
                )}
                {error && (
                  <p className="mt-1 text-sm text-red-600">{error}</p>
                )}
              </div>

              {/* Diagnosis */}
              <div>
                <label
                  htmlFor="diagnosis"
                  className="block text-sm font-medium text-gray-700 mb-1"
                >
                  Diagnosis
                </label>
                <textarea
                  id="diagnosis"
                  name="diagnosis"
                  rows={3}
                  required
                  className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-teal-500 focus:border-teal-500 sm:text-sm"
                  placeholder="Enter diagnosis details"
                  value={diagnosis}
                  onChange={(e) => setDiagnosis(e.target.value)}
                />
              </div>

              {/* Medicines */}
              <div>
                <div className="flex justify-between items-center mb-2">
                  <label className="block text-sm font-medium text-gray-700">
                    Medicines
                  </label>
                  <Button
                    type="button"
                    size="sm"
                    variant="outline"
                    icon={<Plus size={16} />}
                    onClick={handleAddMedicine}
                  >
                    Add Medicine
                  </Button>
                </div>

                {medicines.map((medicine, index) => (
                  <div key={index} className="bg-gray-50 p-4 rounded-md mb-3">
                    <div className="flex justify-between items-center mb-3">
                      <h4 className="text-sm font-medium text-gray-700">
                        Medicine #{index + 1}
                      </h4>
                      {medicines.length > 1 && (
                        <button
                          type="button"
                          onClick={() => handleRemoveMedicine(index)}
                          className="text-red-500 hover:text-red-700"
                        >
                          <Trash2 size={16} />
                        </button>
                      )}
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <label className="block text-xs font-medium text-gray-500 mb-1">
                          Name
                        </label>
                        <input
                          type="text"
                          required
                          className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-teal-500 focus:border-teal-500 sm:text-sm"
                          placeholder="Medicine name"
                          value={medicine.name}
                          onChange={(e) =>
                            handleMedicineChange(index, 'name', e.target.value)
                          }
                        />
                      </div>
                      <div>
                        <label className="block text-xs font-medium text-gray-500 mb-1">
                          Dosage
                        </label>
                        <input
                          type="text"
                          required
                          className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-teal-500 focus:border-teal-500 sm:text-sm"
                          placeholder="e.g., 10mg"
                          value={medicine.dosage}
                          onChange={(e) =>
                            handleMedicineChange(index, 'dosage', e.target.value)
                          }
                        />
                      </div>
                      <div>
                        <label className="block text-xs font-medium text-gray-500 mb-1">
                          Duration
                        </label>
                        <input
                          type="text"
                          required
                          className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-teal-500 focus:border-teal-500 sm:text-sm"
                          placeholder="e.g., 7 days"
                          value={medicine.duration}
                          onChange={(e) =>
                            handleMedicineChange(index, 'duration', e.target.value)
                          }
                        />
                      </div>
                      <div>
                        <label className="block text-xs font-medium text-gray-500 mb-1">
                          Timing
                        </label>
                        <input
                          type="text"
                          required
                          className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-teal-500 focus:border-teal-500 sm:text-sm"
                          placeholder="e.g., After meals"
                          value={medicine.timing}
                          onChange={(e) =>
                            handleMedicineChange(index, 'timing', e.target.value)
                          }
                        />
                      </div>
                    </div>
                  </div>
                ))}
              </div>

              {/* Advice */}
              <div>
                <label
                  htmlFor="advice"
                  className="block text-sm font-medium text-gray-700 mb-1"
                >
                  Advice
                </label>
                <textarea
                  id="advice"
                  name="advice"
                  rows={3}
                  className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-teal-500 focus:border-teal-500 sm:text-sm"
                  placeholder="Enter advice for the patient"
                  value={advice}
                  onChange={(e) => setAdvice(e.target.value)}
                />
              </div>

              {/* Follow-up */}
              <div>
                <label
                  htmlFor="followUp"
                  className="block text-sm font-medium text-gray-700 mb-1"
                >
                  Follow-up Date (Optional)
                </label>
                <input
                  type="date"
                  id="followUp"
                  name="followUp"
                  className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-teal-500 focus:border-teal-500 sm:text-sm"
                  value={followUp}
                  onChange={(e) => setFollowUp(e.target.value)}
                />
              </div>

              {/* Submit Button */}
              <div className="flex justify-end">
                <Button type="submit" variant="primary" disabled={loading}>
                  {loading ? 'Saving...' : 'Save Prescription'}
                </Button>
              </div>
            </div>
          </form>
        </Card>
      </div>
    </DoctorMainLayout>
  );
};

export default DoctorGeneratePrescription;
