{"name": "frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@react-three/drei": "^10.1.2", "@react-three/fiber": "^9.1.2", "@types/jspdf": "^1.3.3", "@types/node": "^22.15.18", "@types/react-router-dom": "^5.3.3", "@types/three": "^0.176.0", "axios": "^1.9.0", "chart.js": "^4.4.1", "gsap": "^3.13.0", "jspdf": "^3.0.1", "lucide-react": "^0.511.0", "react": "^19.1.0", "react-chartjs-2": "^5.2.0", "react-dom": "^19.1.0", "react-intersection-observer": "^9.16.0", "react-router-dom": "^7.6.0", "three": "^0.177.0"}, "devDependencies": {"@eslint/js": "^9.25.0", "@types/react": "^19.1.4", "@types/react-dom": "^19.1.5", "@vitejs/plugin-react": "^4.4.1", "autoprefixer": "^10.4.16", "eslint": "^9.25.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^16.0.0", "postcss": "^8.4.31", "tailwindcss": "^3.3.3", "typescript": "^5.8.3", "vite": "^6.3.5"}}