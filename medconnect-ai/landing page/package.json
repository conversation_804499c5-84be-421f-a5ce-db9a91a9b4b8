{"name": "landing-page", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@react-three/drei": "^10.1.2", "@react-three/fiber": "^9.1.2", "@types/three": "^0.176.0", "autoprefixer": "^10.4.21", "gsap": "^3.13.0", "lucide-react": "^0.511.0", "postcss": "^8.5.4", "react": "^19.1.0", "react-dom": "^19.1.0", "react-intersection-observer": "^9.16.0", "react-router-dom": "^7.6.1", "tailwindcss": "^3.4.17", "three": "^0.177.0"}, "devDependencies": {"@eslint/js": "^9.25.0", "@types/react": "^19.1.2", "@types/react-dom": "^19.1.2", "@types/react-router-dom": "^5.3.3", "@vitejs/plugin-react": "^4.4.1", "eslint": "^9.25.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^16.0.0", "vite": "^6.3.5"}}