import { useEffect } from 'react';
import gsap from 'gsap';
import { ScrollTrigger } from 'gsap/ScrollTrigger';
import { Routes, Route } from 'react-router-dom';

import Header from './components/Header';
import Hero from './components/Hero.tsx';
import HowItWorks from './components/HowItWorks';
import Features from './components/Features.tsx';
import Pricing from './components/Pricing';
import Footer from './components/Footer.tsx';
import ScrollToTop from './components/ScrollToTop';
import Login from './pages/Login';
import SignUp from './pages/SignUp';

function LandingPage() {
  return (
    <>
      <Header />
      <main className="flex-grow">
        <Hero />
        <Features />
        <HowItWorks />
        <Pricing />
      </main>
      <Footer />
      <ScrollToTop />
    </>
  );
}

function App() {
  useEffect(() => {
    // Register ScrollTrigger plugin
    gsap.registerPlugin(ScrollTrigger);

    // Set up animations for elements with the 'fade-in' class
    const fadeInElements = document.querySelectorAll('.fade-in');
    fadeInElements.forEach(element => {
      ScrollTrigger.create({
        trigger: element,
        start: 'top 80%',
        onEnter: () => element.classList.add('visible'),
      });
    });

    // Set up staggered animations for elements with the 'staggered-fade-in' class
    const staggeredContainers = document.querySelectorAll('.staggered-container');
    staggeredContainers.forEach(container => {
      const elements = container.querySelectorAll('.staggered-fade-in');

      ScrollTrigger.create({
        trigger: container,
        start: 'top 80%',
        onEnter: () => {
          gsap.to(elements, {
            opacity: 1,
            y: 0,
            stagger: 0.1,
            duration: 0.6,
            ease: 'power2.out',
          });
        },
      });
    });
  }, []);

  return (
    <Routes>
      <Route path="/" element={<LandingPage />} />
      <Route path="/login" element={<Login />} />
      <Route path="/signup" element={<SignUp />} />
    </Routes>
  );
}

export default App;