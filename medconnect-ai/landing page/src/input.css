@import 'tailwindcss/base';
@import 'tailwindcss/components';
@import 'tailwindcss/utilities';

@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Poppins:wght@400;500;600;700;800&display=swap');

@layer base {
  html {
    scroll-behavior: smooth;
  }
  
  body {
    font-family: 'Inter', sans-serif;
    line-height: 1.6;
  }
  
  h1, h2, h3, h4, h5, h6 {
    font-family: 'Poppins', sans-serif;
  }
}

@layer components {
  .btn-primary {
    @apply bg-medical-blue hover:bg-blue-700 text-white font-semibold py-3 px-6 rounded-lg transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl;
  }
  
  .btn-secondary {
    @apply bg-transparent border-2 border-medical-blue text-medical-blue hover:bg-medical-blue hover:text-white font-semibold py-3 px-6 rounded-lg transition-all duration-300;
  }
  
  .section-padding {
    @apply py-16 md:py-24;
  }
  
  .container-custom {
    @apply max-w-7xl mx-auto px-4 sm:px-6 lg:px-8;
  }
  
  .card-hover {
    @apply transition-all duration-300 transform hover:scale-105 hover:shadow-2xl;
  }
  
  .text-gradient {
    @apply bg-gradient-to-r from-medical-blue to-medical-green bg-clip-text text-transparent;
  }
}

@layer utilities {
  .animation-delay-200 {
    animation-delay: 200ms;
  }
  
  .animation-delay-400 {
    animation-delay: 400ms;
  }
  
  .animation-delay-600 {
    animation-delay: 600ms;
  }
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
  background: #0066CC;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #0052A3;
}

/* Three.js canvas styling */
#three-canvas {
  position: fixed;
  top: 0;
  left: 0;
  z-index: -1;
  pointer-events: none;
}
