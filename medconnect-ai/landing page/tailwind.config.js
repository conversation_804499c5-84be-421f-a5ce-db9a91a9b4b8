/** @type {import('tailwindcss').Config} */
export default {
  content: [
    "./index.html",
    "./src/**/*.{js,ts,jsx,tsx}",
  ],
  theme: {
    extend: {
      colors: {
        'medical-purple': '#8B5CF6',
        'medical-violet': '#A855F7',
        'medical-lavender': '#C4B5FD',
        'medical-light': '#F8FAFC',
        'medical-dark': '#1E293B',
        'accent-purple': '#7C3AED',
        'accent-pink': '#EC4899',
        'primary': '#9333EA',
        'secondary': '#2563EB',
        'purple-50': '#FAF5FF',
        'purple-100': '#F3E8FF',
        'purple-200': '#E9D5FF',
        'purple-300': '#D8B4FE',
        'purple-400': '#C084FC',
        'purple-500': '#A855F7',
        'purple-600': '#9333EA',
        'purple-700': '#7C3AED',
        'purple-800': '#6B21A8',
        'purple-900': '#581C87'
      },
      fontFamily: {
        'sans': ['Inter', 'system-ui', 'sans-serif'],
        'heading': ['Poppins', 'sans-serif']
      },
      animation: {
        'float': 'float 6s ease-in-out infinite',
        'pulse-slow': 'pulse 3s cubic-bezier(0.4, 0, 0.6, 1) infinite',
        'bounce-slow': 'bounce 2s infinite'
      },
      keyframes: {
        float: {
          '0%, 100%': { transform: 'translateY(0px)' },
          '50%': { transform: 'translateY(-20px)' }
        }
      },
      backgroundImage: {
        'gradient-radial': 'radial-gradient(var(--tw-gradient-stops))',
        'medical-gradient': 'linear-gradient(135deg, #0066CC 0%, #00CC66 100%)'
      }
    },
  },
  plugins: [],
}
